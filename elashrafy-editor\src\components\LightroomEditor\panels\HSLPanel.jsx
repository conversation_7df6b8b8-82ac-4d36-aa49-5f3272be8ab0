import { useState } from 'react';

const HSLPanel = ({ adjustments, onUpdateHSLAdjustment }) => {
  const [activeMode, setActiveMode] = useState('hue');

  const colors = [
    { key: 'red', name: 'أحمر', color: '#ff0000' },
    { key: 'orange', name: 'برتقالي', color: '#ff8000' },
    { key: 'yellow', name: 'أصفر', color: '#ffff00' },
    { key: 'green', name: 'أخضر', color: '#00ff00' },
    { key: 'aqua', name: 'سماوي', color: '#00ffff' },
    { key: 'blue', name: 'أزرق', color: '#0000ff' },
    { key: 'purple', name: 'بنفسجي', color: '#8000ff' },
    { key: 'magenta', name: 'أرجواني', color: '#ff00ff' }
  ];

  const modes = [
    { key: 'hue', name: 'التدرج', icon: '🌈' },
    { key: 'saturationHSL', name: 'التشبع', icon: '🎨' },
    { key: 'luminance', name: 'الإضاءة', icon: '💡' }
  ];

  const handleResetColor = (color) => {
    onUpdateHSLAdjustment('hue', color, 0);
    onUpdateHSLAdjustment('saturationHSL', color, 0);
    onUpdateHSLAdjustment('luminance', color, 0);
  };

  const handleResetAll = () => {
    colors.forEach(color => {
      onUpdateHSLAdjustment('hue', color.key, 0);
      onUpdateHSLAdjustment('saturationHSL', color.key, 0);
      onUpdateHSLAdjustment('luminance', color.key, 0);
    });
  };

  const getSliderRange = (mode) => {
    switch (mode) {
      case 'hue':
        return { min: -180, max: 180 };
      case 'saturationHSL':
        return { min: -100, max: 100 };
      case 'luminance':
        return { min: -100, max: 100 };
      default:
        return { min: -100, max: 100 };
    }
  };

  const getSliderClass = (mode) => {
    switch (mode) {
      case 'hue':
        return 'control-slider hue-slider';
      case 'saturationHSL':
        return 'control-slider saturation-slider';
      case 'luminance':
        return 'control-slider luminance-slider';
      default:
        return 'control-slider';
    }
  };

  return (
    <div className="hsl-panel">
      {/* أزرار أنماط HSL */}
      <div className="panel-section">
        <div className="section-title">
          تحكم HSL
          <button 
            className="reset-btn"
            onClick={handleResetAll}
            title="إعادة تعيين جميع قيم HSL"
          >
            إعادة تعيين الكل
          </button>
        </div>

        <div className="hsl-modes">
          {modes.map(mode => (
            <button
              key={mode.key}
              className={`mode-btn ${activeMode === mode.key ? 'active' : ''}`}
              onClick={() => setActiveMode(mode.key)}
              title={mode.name}
            >
              <span className="mode-icon">{mode.icon}</span>
              <span className="mode-name">{mode.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* تحكم الألوان */}
      <div className="panel-section">
        <div className="section-title">
          {modes.find(m => m.key === activeMode)?.name}
        </div>

        <div className="color-controls">
          {colors.map(color => {
            const range = getSliderRange(activeMode);
            const value = adjustments[activeMode][color.key];
            
            return (
              <div key={color.key} className="color-control-group">
                <div className="color-header">
                  <div className="color-info">
                    <div 
                      className="color-swatch"
                      style={{ backgroundColor: color.color }}
                    ></div>
                    <span className="color-name">{color.name}</span>
                  </div>
                  <div className="color-actions">
                    <span className="control-value">
                      {value > 0 ? '+' : ''}{value}
                    </span>
                    <button
                      className="reset-btn small"
                      onClick={() => handleResetColor(color.key)}
                      title={`إعادة تعيين ${color.name}`}
                    >
                      ↺
                    </button>
                  </div>
                </div>

                <input
                  type="range"
                  min={range.min}
                  max={range.max}
                  step="1"
                  value={value}
                  onChange={(e) => onUpdateHSLAdjustment(activeMode, color.key, parseInt(e.target.value))}
                  className={getSliderClass(activeMode)}
                  style={{
                    background: activeMode === 'hue' 
                      ? `linear-gradient(to right, ${color.color}80, ${color.color}, ${color.color}80)`
                      : undefined
                  }}
                />
              </div>
            );
          })}
        </div>
      </div>

      {/* إعدادات سريعة */}
      <div className="panel-section">
        <div className="section-title">تأثيرات سريعة</div>
        
        <div className="quick-effects">
          <button 
            className="effect-btn"
            onClick={() => {
              // تأثير السماء الزرقاء
              onUpdateHSLAdjustment('saturationHSL', 'blue', 30);
              onUpdateHSLAdjustment('luminance', 'blue', -10);
            }}
          >
            سماء زرقاء
          </button>
          
          <button 
            className="effect-btn"
            onClick={() => {
              // تأثير الخضرة الطبيعية
              onUpdateHSLAdjustment('saturationHSL', 'green', 25);
              onUpdateHSLAdjustment('luminance', 'green', 5);
            }}
          >
            خضرة طبيعية
          </button>

          <button 
            className="effect-btn"
            onClick={() => {
              // تأثير الغروب الذهبي
              onUpdateHSLAdjustment('hue', 'orange', 10);
              onUpdateHSLAdjustment('saturationHSL', 'orange', 40);
              onUpdateHSLAdjustment('saturationHSL', 'yellow', 30);
            }}
          >
            غروب ذهبي
          </button>

          <button 
            className="effect-btn"
            onClick={() => {
              // تأثير البشرة الدافئة
              onUpdateHSLAdjustment('hue', 'orange', 5);
              onUpdateHSLAdjustment('saturationHSL', 'orange', 15);
              onUpdateHSLAdjustment('luminance', 'orange', 10);
            }}
          >
            بشرة دافئة
          </button>

          <button 
            className="effect-btn"
            onClick={() => {
              // تأثير أحادي اللون
              colors.forEach(color => {
                if (color.key !== 'red') {
                  onUpdateHSLAdjustment('saturationHSL', color.key, -80);
                }
              });
            }}
          >
            أحادي أحمر
          </button>

          <button 
            className="effect-btn"
            onClick={() => {
              // تأثير الألوان الباردة
              onUpdateHSLAdjustment('saturationHSL', 'blue', 20);
              onUpdateHSLAdjustment('saturationHSL', 'aqua', 25);
              onUpdateHSLAdjustment('saturationHSL', 'red', -30);
              onUpdateHSLAdjustment('saturationHSL', 'orange', -30);
            }}
          >
            ألوان باردة
          </button>
        </div>
      </div>

      {/* معلومات مفيدة */}
      <div className="panel-section">
        <div className="section-title">نصائح</div>
        <div className="tips">
          <div className="tip">
            <strong>التدرج:</strong> يغير لون الكائنات
          </div>
          <div className="tip">
            <strong>التشبع:</strong> يزيد أو يقلل كثافة الألوان
          </div>
          <div className="tip">
            <strong>الإضاءة:</strong> يفتح أو يغمق الألوان المحددة
          </div>
        </div>
      </div>
    </div>
  );
};

export default HSLPanel;
