import { useState, useRef, useCallback, useEffect } from 'react';
import ImageCanvas from './ImageCanvas';
import ControlPanels from './ControlPanels';
import Toolbar from './Toolbar';
import PresetPanel from './PresetPanel';
import HistoryPanel from './HistoryPanel';

import { ImageProcessor } from './utils/ImageProcessor';
import './LightroomEditor.css';

const LightroomEditor = ({ isOpen, onClose, onSave, onBackToWelcome }) => {
  // حالة الصورة الأساسية
  const [originalImage, setOriginalImage] = useState(null);
  const [processedImage, setProcessedImage] = useState(null);
  const [imageData, setImageData] = useState(null);
  const [fileName, setFileName] = useState('');
  
  // حالة التعديلات
  const [adjustments, setAdjustments] = useState({
    // تعديلات الإضاءة
    exposure: 0,
    highlights: 0,
    shadows: 0,
    whites: 0,
    blacks: 0,
    
    // تعديلات الألوان
    brightness: 0,
    contrast: 0,
    saturation: 0,
    vibrance: 0,
    
    // توازن الأبيض
    temperature: 0,
    tint: 0,
    
    // HSL
    hue: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
    saturationHSL: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
    luminance: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
    
    // تحسينات
    clarity: 0,
    dehaze: 0,
    noiseReduction: 0,
    sharpening: 0,
    
    // تحويلات
    cropX: 0,
    cropY: 0,
    cropWidth: 100,
    cropHeight: 100,
    rotation: 0,
    flipHorizontal: false,
    flipVertical: false
  });
  
  // حالة التاريخ والتراجع
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  
  // حالة واجهة المستخدم
  const [activePanel, setActivePanel] = useState('basic');
  const [showBeforeAfter, setShowBeforeAfter] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  // المراجع
  const canvasRef = useRef(null);
  const fileInputRef = useRef(null);
  const imageProcessor = useRef(new ImageProcessor());
  
  // معالجة الصورة فائقة السرعة
  const processImage = useCallback(async () => {
    if (!originalImage || isProcessing) return;

    setIsProcessing(true);

    try {
      const processed = await imageProcessor.current.processImage(originalImage, adjustments);
      setProcessedImage(processed);
      setImageData(processed);
    } catch (error) {
      console.error('خطأ في معالجة الصورة:', error);
      setProcessedImage(originalImage);
    } finally {
      // إنهاء فوري للمعالجة
      setIsProcessing(false);
    }
  }, [originalImage, adjustments]);



  // تحديث الصورة عند تغيير التعديلات
  useEffect(() => {
    if (originalImage && !isProcessing) {
      processImage();
    }
  }, [adjustments, originalImage, processImage]);

  // تنظيف الموارد عند إغلاق المحرر
  useEffect(() => {
    return () => {
      if (imageProcessor.current) {
        imageProcessor.current.cleanup();
      }
    };
  }, []);
  
  // فتح ملف
  const handleOpenFile = () => {
    fileInputRef.current?.click();
  };
  
  // معالجة تحميل الملف
  const handleFileLoad = (event) => {
    const file = event.target.files[0];
    if (!file || !file.type.startsWith('image/')) {
      alert('يرجى اختيار ملف صورة صالح');
      return;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new Image();
      img.onload = () => {
        // إعادة تعيين معالج الصور للصورة الجديدة
        imageProcessor.current.resetOriginalData();

        setOriginalImage(img);
        setProcessedImage(img);
        setImageData(img);
        setFileName(file.name);

        // إعادة تعيين التعديلات
        const defaultAdjustments = {
          exposure: 0, highlights: 0, shadows: 0, whites: 0, blacks: 0,
          brightness: 0, contrast: 0, saturation: 0, vibrance: 0,
          temperature: 0, tint: 0,
          hue: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
          saturationHSL: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
          luminance: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
          clarity: 0, dehaze: 0, noiseReduction: 0, sharpening: 0,
          cropX: 0, cropY: 0, cropWidth: 100, cropHeight: 100,
          rotation: 0, flipHorizontal: false, flipVertical: false
        };

        // إعادة تعيين التاريخ أولاً
        setHistory([]);
        setHistoryIndex(-1);

        // ثم تعيين التعديلات
        setAdjustments(defaultAdjustments);

        // إضافة للتاريخ بعد تأخير قصير
        setTimeout(() => {
          addToHistory('فتح الصورة');
        }, 100);
      };
      img.src = e.target.result;
    };
    reader.readAsDataURL(file);
    event.target.value = '';
  };
  
  // تحديث التعديلات
  const updateAdjustment = useCallback((key, value) => {
    setAdjustments(prev => {
      const newAdjustments = { ...prev, [key]: value };
      return newAdjustments;
    });
  }, []);
  
  // تحديث تعديلات HSL
  const updateHSLAdjustment = useCallback((type, color, value) => {
    setAdjustments(prev => ({
      ...prev,
      [type]: { ...prev[type], [color]: value }
    }));
  }, []);
  
  // إضافة للتاريخ
  const addToHistory = useCallback((action) => {
    const newHistoryItem = {
      id: Date.now(),
      action,
      adjustments: { ...adjustments },
      timestamp: new Date()
    };

    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1);
      newHistory.push(newHistoryItem);
      return newHistory;
    });
    setHistoryIndex(prev => prev + 1);
  }, [adjustments, historyIndex]);
  
  // التراجع
  const undo = () => {
    if (historyIndex > 0) {
      const previousState = history[historyIndex - 1];
      setAdjustments(previousState.adjustments);
      setHistoryIndex(prev => prev - 1);
    }
  };
  
  // الإعادة
  const redo = () => {
    if (historyIndex < history.length - 1) {
      const nextState = history[historyIndex + 1];
      setAdjustments(nextState.adjustments);
      setHistoryIndex(prev => prev + 1);
    }
  };
  
  // إعادة تعيين جميع التعديلات
  const resetAdjustments = () => {
    setAdjustments({
      exposure: 0, highlights: 0, shadows: 0, whites: 0, blacks: 0,
      brightness: 0, contrast: 0, saturation: 0, vibrance: 0,
      temperature: 0, tint: 0,
      hue: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
      saturationHSL: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
      luminance: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
      clarity: 0, dehaze: 0, noiseReduction: 0, sharpening: 0,
      cropX: 0, cropY: 0, cropWidth: 100, cropHeight: 100,
      rotation: 0, flipHorizontal: false, flipVertical: false
    });
    addToHistory('إعادة تعيين التعديلات');
  };
  
  // حفظ الصورة
  const handleSave = (format = 'jpeg', quality = 90) => {
    if (!processedImage || !canvasRef.current) return;
    
    const canvas = canvasRef.current.getCanvas();
    const mimeType = `image/${format}`;
    
    canvas.toBlob((blob) => {
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${fileName.split('.')[0]}_edited.${format}`;
      a.click();
      URL.revokeObjectURL(url);
    }, mimeType, format === 'jpeg' ? quality / 100 : undefined);
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="lightroom-editor" dir="rtl">
      {/* شريط الأدوات العلوي */}
      <Toolbar
        fileName={fileName}
        onOpenFile={handleOpenFile}
        onSave={handleSave}
        onClose={onClose}
        onBackToWelcome={onBackToWelcome}
        onUndo={undo}
        onRedo={redo}
        onReset={resetAdjustments}
        canUndo={historyIndex > 0}
        canRedo={historyIndex < history.length - 1}
        showBeforeAfter={showBeforeAfter}
        onToggleBeforeAfter={() => setShowBeforeAfter(!showBeforeAfter)}
      />
      
      <div className="editor-content">
        {/* منطقة الصورة الرئيسية */}
        <div className="image-area">
          <ImageCanvas
            ref={canvasRef}
            processedImage={showBeforeAfter ? originalImage : processedImage}
            isProcessing={isProcessing}
          />

          {/* مؤشر صغير للمعالجة */}
          {isProcessing && (
            <div className="mini-processing-indicator">
              <div className="mini-spinner"></div>
            </div>
          )}
        </div>

        {/* اللوحات الجانبية */}
        <div className="side-panels">
          {/* لوحة الإعدادات المسبقة */}
          <PresetPanel
            onApplyPreset={(presetAdjustments) => {
              console.log('تطبيق إعداد مسبق:', presetAdjustments);
              setAdjustments(presetAdjustments);
              addToHistory('تطبيق إعداد مسبق');
            }}
          />
          
          {/* لوحات التحكم */}
          <ControlPanels
            activePanel={activePanel}
            onPanelChange={setActivePanel}
            adjustments={adjustments}
            onUpdateAdjustment={updateAdjustment}
            onUpdateHSLAdjustment={updateHSLAdjustment}
          />
          
          {/* لوحة التاريخ */}
          <HistoryPanel
            history={history}
            historyIndex={historyIndex}
            onGoToHistory={(index) => {
              if (index >= 0 && index < history.length) {
                setAdjustments(history[index].adjustments);
                setHistoryIndex(index);
              }
            }}
          />
        </div>
      </div>
      
      {/* مدخل الملفات المخفي */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileLoad}
        style={{ display: 'none' }}
      />
    </div>
  );
};

export default LightroomEditor;
