const TransformPanel = ({ adjustments, onUpdateAdjustment }) => {
  const handleResetCrop = () => {
    onUpdateAdjustment('cropX', 0);
    onUpdateAdjustment('cropY', 0);
    onUpdateAdjustment('cropWidth', 100);
    onUpdateAdjustment('cropHeight', 100);
  };

  const handleResetRotation = () => {
    onUpdateAdjustment('rotation', 0);
  };

  const handleResetFlip = () => {
    onUpdateAdjustment('flipHorizontal', false);
    onUpdateAdjustment('flipVertical', false);
  };

  const handleRotate = (degrees) => {
    const newRotation = (adjustments.rotation + degrees) % 360;
    onUpdateAdjustment('rotation', newRotation);
  };

  const cropPresets = [
    { name: 'مربع', ratio: '1:1', width: 100, height: 100 },
    { name: '4:3', ratio: '4:3', width: 100, height: 75 },
    { name: '16:9', ratio: '16:9', width: 100, height: 56.25 },
    { name: '3:2', ratio: '3:2', width: 100, height: 66.67 },
    { name: '5:4', ratio: '5:4', width: 100, height: 80 },
    { name: 'حر', ratio: 'حر', width: 100, height: 100 }
  ];

  return (
    <div className="transform-panel">
      {/* قسم القص */}
      <div className="panel-section">
        <div className="section-title">
          القص والتأطير
          <button 
            className="reset-btn"
            onClick={handleResetCrop}
            title="إعادة تعيين القص"
          >
            إعادة تعيين
          </button>
        </div>

        <div className="crop-presets">
          <div className="preset-grid">
            {cropPresets.map((preset, index) => (
              <button
                key={index}
                className="crop-preset-btn"
                onClick={() => {
                  onUpdateAdjustment('cropWidth', preset.width);
                  onUpdateAdjustment('cropHeight', preset.height);
                }}
                title={`تطبيق نسبة ${preset.ratio}`}
              >
                <div className="preset-visual">
                  <div 
                    className="preset-rect"
                    style={{
                      width: `${Math.min(preset.width / 100 * 30, 30)}px`,
                      height: `${Math.min(preset.height / 100 * 30, 30)}px`
                    }}
                  ></div>
                </div>
                <span className="preset-label">{preset.name}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="crop-controls">
          <div className="control-row">
            <div className="control-group small">
              <label className="control-label">X</label>
              <input
                type="number"
                min="0"
                max="100"
                value={adjustments.cropX}
                onChange={(e) => onUpdateAdjustment('cropX', parseInt(e.target.value) || 0)}
                className="number-input"
              />
            </div>
            
            <div className="control-group small">
              <label className="control-label">Y</label>
              <input
                type="number"
                min="0"
                max="100"
                value={adjustments.cropY}
                onChange={(e) => onUpdateAdjustment('cropY', parseInt(e.target.value) || 0)}
                className="number-input"
              />
            </div>
          </div>

          <div className="control-row">
            <div className="control-group small">
              <label className="control-label">العرض</label>
              <input
                type="number"
                min="1"
                max="100"
                value={adjustments.cropWidth}
                onChange={(e) => onUpdateAdjustment('cropWidth', parseInt(e.target.value) || 100)}
                className="number-input"
              />
            </div>
            
            <div className="control-group small">
              <label className="control-label">الارتفاع</label>
              <input
                type="number"
                min="1"
                max="100"
                value={adjustments.cropHeight}
                onChange={(e) => onUpdateAdjustment('cropHeight', parseInt(e.target.value) || 100)}
                className="number-input"
              />
            </div>
          </div>
        </div>
      </div>

      {/* قسم التدوير */}
      <div className="panel-section">
        <div className="section-title">
          التدوير والاستقامة
          <button 
            className="reset-btn"
            onClick={handleResetRotation}
            title="إعادة تعيين التدوير"
          >
            إعادة تعيين
          </button>
        </div>

        <div className="rotation-controls">
          <div className="control-group">
            <div className="control-label">
              <span className="control-name">الزاوية</span>
              <span className="control-value">{adjustments.rotation}°</span>
            </div>
            <input
              type="range"
              min="-180"
              max="180"
              step="0.1"
              value={adjustments.rotation}
              onChange={(e) => onUpdateAdjustment('rotation', parseFloat(e.target.value))}
              className="control-slider"
            />
          </div>

          <div className="rotation-buttons">
            <button 
              className="rotation-btn"
              onClick={() => handleRotate(-90)}
              title="تدوير 90° يساراً"
            >
              ↶ 90°
            </button>
            
            <button 
              className="rotation-btn"
              onClick={() => handleRotate(90)}
              title="تدوير 90° يميناً"
            >
              ↷ 90°
            </button>
            
            <button 
              className="rotation-btn"
              onClick={() => handleRotate(180)}
              title="تدوير 180°"
            >
              ↻ 180°
            </button>
          </div>

          <div className="fine-rotation">
            <button 
              className="fine-btn"
              onClick={() => handleRotate(-0.1)}
              title="تدوير دقيق يساراً"
            >
              ← 0.1°
            </button>
            
            <button 
              className="fine-btn"
              onClick={() => handleRotate(0.1)}
              title="تدوير دقيق يميناً"
            >
              → 0.1°
            </button>
          </div>
        </div>
      </div>

      {/* قسم الانعكاس */}
      <div className="panel-section">
        <div className="section-title">
          الانعكاس
          <button 
            className="reset-btn"
            onClick={handleResetFlip}
            title="إعادة تعيين الانعكاس"
          >
            إعادة تعيين
          </button>
        </div>

        <div className="flip-controls">
          <div className="flip-buttons">
            <button 
              className={`flip-btn ${adjustments.flipHorizontal ? 'active' : ''}`}
              onClick={() => onUpdateAdjustment('flipHorizontal', !adjustments.flipHorizontal)}
              title="انعكاس أفقي"
            >
              <span className="flip-icon">↔️</span>
              <span className="flip-label">أفقي</span>
            </button>
            
            <button 
              className={`flip-btn ${adjustments.flipVertical ? 'active' : ''}`}
              onClick={() => onUpdateAdjustment('flipVertical', !adjustments.flipVertical)}
              title="انعكاس عمودي"
            >
              <span className="flip-icon">↕️</span>
              <span className="flip-label">عمودي</span>
            </button>
          </div>
        </div>
      </div>

      {/* إعدادات سريعة */}
      <div className="panel-section">
        <div className="section-title">إعدادات سريعة</div>
        
        <div className="quick-transforms">
          <button 
            className="transform-btn"
            onClick={() => {
              onUpdateAdjustment('cropWidth', 100);
              onUpdateAdjustment('cropHeight', 100);
              onUpdateAdjustment('rotation', 0);
            }}
          >
            📐 مربع مثالي
          </button>
          
          <button 
            className="transform-btn"
            onClick={() => {
              onUpdateAdjustment('cropWidth', 100);
              onUpdateAdjustment('cropHeight', 56.25);
              onUpdateAdjustment('rotation', 0);
            }}
          >
            📺 عريض 16:9
          </button>

          <button 
            className="transform-btn"
            onClick={() => {
              onUpdateAdjustment('cropWidth', 100);
              onUpdateAdjustment('cropHeight', 75);
              onUpdateAdjustment('rotation', 0);
            }}
          >
            📷 كلاسيكي 4:3
          </button>

          <button 
            className="transform-btn"
            onClick={() => {
              onUpdateAdjustment('rotation', 0);
              onUpdateAdjustment('flipHorizontal', false);
              onUpdateAdjustment('flipVertical', false);
            }}
          >
            🔄 استقامة
          </button>
        </div>
      </div>

      {/* معلومات التحويل */}
      <div className="panel-section">
        <div className="section-title">معلومات</div>
        
        <div className="transform-info">
          <div className="info-item">
            <span className="info-label">النسبة الحالية:</span>
            <span className="info-value">
              {(adjustments.cropWidth / adjustments.cropHeight).toFixed(2)}:1
            </span>
          </div>
          
          <div className="info-item">
            <span className="info-label">الزاوية:</span>
            <span className="info-value">{adjustments.rotation}°</span>
          </div>
          
          <div className="info-item">
            <span className="info-label">الانعكاس:</span>
            <span className="info-value">
              {adjustments.flipHorizontal && adjustments.flipVertical ? 'أفقي + عمودي' :
               adjustments.flipHorizontal ? 'أفقي' :
               adjustments.flipVertical ? 'عمودي' : 'لا يوجد'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransformPanel;
