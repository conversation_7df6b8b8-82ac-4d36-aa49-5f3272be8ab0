.image-properties {
  padding: 16px;
  background: white;
  direction: rtl;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.properties-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;
}

.properties-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.reset-button {
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #6c757d;
  transition: all 0.2s ease;
}

.reset-button:hover {
  background: #e9ecef;
  color: #495057;
}

.properties-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.property-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.property-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 8px;
}

.property-group {
  margin-bottom: 16px;
}

.property-group:last-child {
  margin-bottom: 0;
}

.property-group label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 6px;
}

.rotation-controls,
.scale-control,
.color-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-input {
  flex: 1;
  height: 4px;
  border-radius: 2px;
  background: #dee2e6;
  outline: none;
  -webkit-appearance: none;
}

.range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #007acc;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-input::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #007acc;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.value-display {
  font-size: 11px;
  font-weight: 600;
  color: #007acc;
  min-width: 40px;
  text-align: center;
  background: white;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #dee2e6;
}

.quick-rotation {
  display: flex;
  gap: 4px;
  margin-top: 8px;
}

.quick-rotation button {
  flex: 1;
  padding: 6px 8px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.quick-rotation button:hover {
  background: #e3f2fd;
  border-color: #2196f3;
}

.flip-controls {
  display: flex;
  gap: 8px;
}

.flip-button {
  flex: 1;
  padding: 8px 12px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.flip-button:hover {
  background: #e3f2fd;
  border-color: #2196f3;
}

.flip-button.active {
  background: #2196f3;
  border-color: #1976d2;
  color: white;
}

.link-button {
  width: 100%;
  padding: 8px 12px;
  background: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #1976d2;
  transition: all 0.2s ease;
}

.link-button:hover {
  background: #bbdefb;
}

.apply-button {
  width: 100%;
  padding: 12px 16px;
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  color: white;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.apply-button:hover {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
}

.apply-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .image-properties {
    padding: 12px;
  }
  
  .properties-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .reset-button {
    width: 100%;
  }
  
  .property-section {
    padding: 12px;
  }
  
  .property-section h4 {
    font-size: 13px;
  }
  
  .flip-controls,
  .quick-rotation {
    flex-direction: column;
  }
  
  .rotation-controls,
  .scale-control,
  .color-control {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
  
  .value-display {
    text-align: center;
    min-width: auto;
  }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .image-properties {
    background: #252526;
    color: #cccccc;
  }
  
  .properties-header {
    border-color: #3e3e42;
  }
  
  .properties-header h3 {
    color: #cccccc;
  }
  
  .reset-button {
    background: #383838;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .reset-button:hover {
    background: #3e3e42;
  }
  
  .property-section {
    background: #2d2d30;
    border-color: #3e3e42;
  }
  
  .property-section h4 {
    color: #cccccc;
    border-color: #3e3e42;
  }
  
  .property-group label {
    color: #cccccc;
  }
  
  .range-input {
    background: #3e3e42;
  }
  
  .value-display {
    background: #383838;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .quick-rotation button,
  .flip-button {
    background: #383838;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .quick-rotation button:hover,
  .flip-button:hover {
    background: #1e3a8a;
    border-color: #2196f3;
  }
  
  .link-button {
    background: #1e3a8a;
    border-color: #2196f3;
    color: #93c5fd;
  }
  
  .link-button:hover {
    background: #1e40af;
  }
}

/* تحسينات إمكانية الوصول */
.range-input:focus {
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.3);
}

.flip-button:focus,
.quick-rotation button:focus,
.link-button:focus,
.apply-button:focus,
.reset-button:focus {
  outline: 2px solid #007acc;
  outline-offset: 2px;
}
