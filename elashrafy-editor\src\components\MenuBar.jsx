import React from 'react';
import './MenuBar.css';

const MenuBar = ({
  onNew,
  onOpen,
  onSave,
  onSaveAs,
  onCut,
  onCopy,
  onPaste,
  onUndo,
  onRedo,
  onFind,
  onReplace,
  onOpenImageEditor,
  canUndo,
  canRedo
}) => {
  return (
    <div className="menu-bar">
      <div className="menu-group">
        <span className="menu-title">ملف</span>
        <div className="menu-items">
          <button className="menu-item" onClick={onNew} title="ملف جديد (Ctrl+N)">
            <span className="icon">📄</span>
            جديد
          </button>
          <button className="menu-item" onClick={onOpen} title="فتح ملف (Ctrl+O)">
            <span className="icon">📁</span>
            فتح
          </button>
          <button className="menu-item" onClick={onSave} title="حفظ (Ctrl+S)">
            <span className="icon">💾</span>
            حفظ
          </button>
          <button className="menu-item" onClick={onSaveAs} title="حفظ باسم (Ctrl+Shift+S)">
            <span className="icon">📝</span>
            حفظ باسم
          </button>
        </div>
      </div>

      <div className="menu-group">
        <span className="menu-title">تحرير</span>
        <div className="menu-items">
          <button 
            className={`menu-item ${!canUndo ? 'disabled' : ''}`} 
            onClick={onUndo} 
            disabled={!canUndo}
            title="تراجع (Ctrl+Z)"
          >
            <span className="icon">↶</span>
            تراجع
          </button>
          <button 
            className={`menu-item ${!canRedo ? 'disabled' : ''}`} 
            onClick={onRedo} 
            disabled={!canRedo}
            title="إعادة (Ctrl+Y)"
          >
            <span className="icon">↷</span>
            إعادة
          </button>
          <div className="separator"></div>
          <button className="menu-item" onClick={onCut} title="قص (Ctrl+X)">
            <span className="icon">✂️</span>
            قص
          </button>
          <button className="menu-item" onClick={onCopy} title="نسخ (Ctrl+C)">
            <span className="icon">📋</span>
            نسخ
          </button>
          <button className="menu-item" onClick={onPaste} title="لصق (Ctrl+V)">
            <span className="icon">📄</span>
            لصق
          </button>
        </div>
      </div>

      <div className="menu-group">
        <span className="menu-title">بحث</span>
        <div className="menu-items">
          <button className="menu-item" onClick={onFind} title="بحث (Ctrl+F)">
            <span className="icon">🔍</span>
            بحث
          </button>
          <button className="menu-item" onClick={onReplace} title="استبدال (Ctrl+H)">
            <span className="icon">🔄</span>
            استبدال
          </button>
        </div>
      </div>

      <div className="menu-group">
        <span className="menu-title">صور</span>
        <div className="menu-items">
          <button className="menu-item" onClick={onOpenImageEditor} title="محرر الصور (Ctrl+I)">
            <span className="icon">🖼️</span>
            محرر الصور
          </button>
        </div>
      </div>
    </div>
  );
};

export default MenuBar;
