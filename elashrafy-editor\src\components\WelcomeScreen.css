.welcome-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
  display: grid;
  grid-template-rows: auto 1fr auto;
  grid-template-areas:
    "header"
    "main"
    "footer";
  z-index: 3000;
  direction: rtl;
  overflow: hidden;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.close-welcome {
  position: fixed;
  top: 30px;
  right: 30px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  color: white;
  z-index: 10;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.close-welcome:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

.welcome-header {
  grid-area: header;
  text-align: center;
  padding: 15px 30px;
  background: transparent;
  color: white;
  position: relative;
  overflow: hidden;
}

/* تصميم اللوجو */
.welcome-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
  animation: logoAppear 1s ease-out;
}

.logo-icon {
  display: flex;
  gap: 3px;
  font-size: 2rem;
  animation: iconFloat 3s ease-in-out infinite;
}

.icon-text, .icon-image {
  filter: drop-shadow(0 4px 8px rgba(74, 144, 226, 0.3));
}

.logo-text {
  text-align: center;
}

.brand-name {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(45deg, #4a90e2, #5ba0f2, #6bb0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  font-family: 'Cairo', sans-serif;
}

.brand-subtitle {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 3px 0 0 0;
  font-weight: 400;
}

@keyframes logoAppear {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* تصميم العناوين */
.welcome-title {
  font-size: 1.6rem;
  font-weight: 700;
  margin: 0 0 10px 0;
  background: linear-gradient(45deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  animation: slideIn 0.6s ease-out;
}

.welcome-subtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.5;
  animation: slideIn 0.8s ease-out 0.2s both;
}

.welcome-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.welcome-logo {
  position: relative;
  z-index: 2;
}

.logo-icon {
  font-size: 60px;
  display: block;
  margin-bottom: 15px;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.welcome-logo h1 {
  margin: 0;
  font-size: 36px;
  font-weight: 700;
  text-shadow: 0 4px 8px rgba(0,0,0,0.5);
  margin-bottom: 8px;
  font-family: 'Cairo', sans-serif;
  letter-spacing: 1px;
}

.logo-subtitle {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
  font-weight: 400;
  font-family: 'Cairo', sans-serif;
}

.welcome-badge {
  position: relative;
  display: inline-block;
  margin-top: 20px;
  background: rgba(255, 255, 255, 0.15);
  padding: 12px 25px;
  border-radius: 25px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.badge-text {
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'Cairo', sans-serif;
}

.badge-sparkle {
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 24px;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.3) rotate(180deg); opacity: 0.7; }
}

.welcome-content {
  grid-area: main;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-areas:
    "description actions"
    "features features";
  gap: 30px;
  padding: 15px 30px;
  max-width: 1200px;
  margin: 0 auto;
  align-items: start;
  overflow-y: auto;
}

.welcome-description {
  grid-area: description;
  text-align: right;
  color: white;
  animation: slideIn 0.8s ease-out 0.2s both;
}

.welcome-description h2 {
  margin: 0 0 15px;
  font-size: 28px;
  color: white;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'Cairo', sans-serif;
}

.welcome-description p {
  margin: 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.7;
  font-family: 'Cairo', sans-serif;
}

.quick-actions {
  grid-area: actions;
  display: flex;
  flex-direction: column;
  gap: 20px;
  animation: slideIn 0.8s ease-out 0.4s both;
}

.quick-actions h3 {
  margin: 0 0 25px;
  font-size: 24px;
  color: white;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'Cairo', sans-serif;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px 25px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: right;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  text-align: right;
}

.action-btn.primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.4);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-color: rgba(255, 255, 255, 0.35);
}

.action-btn.tertiary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.action-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
}

.btn-icon {
  font-size: 32px;
  flex-shrink: 0;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: right;
  flex: 1;
}

.btn-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  font-family: 'Cairo', sans-serif;
}

.btn-desc {
  font-size: 13px;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-family: 'Cairo', sans-serif;
}

.features-preview {
  grid-area: features;
  margin-top: 40px;
}

.features-preview h3 {
  margin: 0 0 30px;
  font-size: 26px;
  color: white;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'Cairo', sans-serif;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 25px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  font-size: 20px;
  flex-shrink: 0;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.feature-text {
  font-size: 14px;
  color: white;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-family: 'Cairo', sans-serif;
}

.welcome-footer {
  grid-area: footer;
  text-align: center;
  padding: 25px 40px 35px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 1024px) {
  .welcome-content {
    grid-template-columns: 1fr;
    grid-template-areas:
      "description"
      "actions"
      "features";
    gap: 40px;
    padding: 40px 60px;
  }

  .welcome-description {
    text-align: center;
  }
}

@media (max-width: 768px) {
  .welcome-header {
    padding: 40px 20px 30px;
  }

  .welcome-logo h1 {
    font-size: 36px;
  }

  .logo-icon {
    font-size: 60px;
  }

  .welcome-content {
    padding: 30px 20px;
    gap: 30px;
  }

  .welcome-description h2 {
    font-size: 28px;
  }

  .welcome-description p {
    font-size: 16px;
  }

  .quick-actions h3 {
    font-size: 24px;
  }

  .action-btn {
    padding: 20px 25px;
  }

  .btn-title {
    font-size: 18px;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }

  .feature-item {
    padding: 15px 20px;
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .feature-icon {
    font-size: 20px;
  }

  .feature-text {
    font-size: 14px;
  }
}
