.welcome-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
  display: grid;
  grid-template-rows: auto 1fr auto;
  grid-template-areas:
    "header"
    "main"
    "footer";
  z-index: 3000;
  direction: rtl;
  overflow: hidden;
  height: 100vh;
  padding: 10px;
  box-sizing: border-box;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.close-welcome {
  position: fixed;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  color: white;
  z-index: 10;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.close-welcome:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

.welcome-header {
  grid-area: header;
  text-align: center;
  padding: 8px 15px;
  background: transparent;
  color: white;
  position: relative;
  overflow: hidden;
}

/* تصميم اللوجو */
.welcome-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 10px;
  animation: logoAppear 1s ease-out;
}

.logo-icon {
  display: flex;
  gap: 2px;
  font-size: 1.4rem;
  animation: iconFloat 3s ease-in-out infinite;
  padding: 4px;
  border-radius: 8px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b6b);
  background-size: 400% 400%;
  animation: iconFloat 3s ease-in-out infinite, colorShift 4s ease-in-out infinite;
}

.icon-text, .icon-image {
  filter: drop-shadow(0 4px 8px rgba(74, 144, 226, 0.3));
}

.logo-text {
  text-align: center;
}

.brand-name {
  font-size: 1.3rem;
  font-weight: 700;
  background: linear-gradient(45deg, #4a90e2, #5ba0f2, #6bb0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  font-family: 'Cairo', sans-serif;
}

.brand-subtitle {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 2px 0 0 0;
  font-weight: 400;
}

@keyframes logoAppear {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* تصميم العناوين */
.welcome-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0 0 6px 0;
  background: linear-gradient(45deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  animation: slideIn 0.6s ease-out;
}

.welcome-subtitle {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.4;
  animation: slideIn 0.8s ease-out 0.2s both;
}

.welcome-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.welcome-logo {
  position: relative;
  z-index: 2;
}

.logo-icon {
  font-size: 60px;
  display: block;
  margin-bottom: 15px;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.welcome-logo h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  text-shadow: 0 4px 8px rgba(0,0,0,0.5);
  margin-bottom: 4px;
  font-family: 'Cairo', sans-serif;
  letter-spacing: 1px;
}

.logo-subtitle {
  margin: 0;
  font-size: 12px;
  opacity: 0.9;
  font-weight: 400;
  font-family: 'Cairo', sans-serif;
}

.welcome-badge {
  position: relative;
  display: inline-block;
  margin-top: 8px;
  background: rgba(255, 255, 255, 0.15);
  padding: 6px 15px;
  border-radius: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.badge-text {
  font-size: 12px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'Cairo', sans-serif;
}

.badge-sparkle {
  position: absolute;
  top: -6px;
  right: -6px;
  font-size: 16px;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.3) rotate(180deg); opacity: 0.7; }
}

.welcome-content {
  grid-area: main;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-areas:
    "description actions"
    "features features";
  gap: 15px;
  padding: 8px 15px;
  max-width: 1200px;
  margin: 0 auto;
  align-items: start;
  overflow: hidden;
}

.welcome-description {
  grid-area: description;
  text-align: right;
  color: white;
  animation: slideIn 0.8s ease-out 0.2s both;
}

.welcome-description h2 {
  margin: 0 0 8px;
  font-size: 18px;
  color: white;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'Cairo', sans-serif;
}

.welcome-description p {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  font-family: 'Cairo', sans-serif;
}

.welcome-image-container {
  display: inline-flex;
  justify-content: center;
  margin-top: 15px;
  position: relative;
  padding: 4px;
  border-radius: 12px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b6b);
  background-size: 400% 400%;
  animation: colorShift 4s ease-in-out infinite;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

.welcome-image {
  max-width: 100%;
  max-height: 180px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  border: none;
  transition: all 0.3s ease;
  object-fit: cover;
  display: block;
}

.welcome-image:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.welcome-image-container:hover {
  animation-duration: 2s;
}

@keyframes colorShift {
  0% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.quick-actions {
  grid-area: actions;
  display: flex;
  flex-direction: column;
  gap: 10px;
  animation: slideIn 0.8s ease-out 0.4s both;
}

.quick-actions h3 {
  margin: 0 0 12px;
  font-size: 16px;
  color: white;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'Cairo', sans-serif;
}

.action-buttons {
  display: flex;
  flex-direction: row;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: right;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  text-align: right;
  flex: 1;
  min-width: 140px;
  max-width: 180px;
}

.action-btn.primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.4);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-color: rgba(255, 255, 255, 0.35);
}

.action-btn.tertiary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
}

.btn-icon {
  font-size: 20px;
  flex-shrink: 0;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  padding: 3px;
  border-radius: 8px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b6b);
  background-size: 400% 400%;
  animation: colorShift 4s ease-in-out infinite;
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: right;
  flex: 1;
}

.btn-title {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 2px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  font-family: 'Cairo', sans-serif;
}

.btn-desc {
  font-size: 10px;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-family: 'Cairo', sans-serif;
}

.features-preview {
  grid-area: features;
  margin-top: 15px;
}

.features-preview h3 {
  margin: 0 0 12px;
  font-size: 16px;
  color: white;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  font-family: 'Cairo', sans-serif;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  font-size: 14px;
  flex-shrink: 0;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  padding: 2px;
  border-radius: 6px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b6b);
  background-size: 400% 400%;
  animation: colorShift 4s ease-in-out infinite;
}

.feature-text {
  font-size: 10px;
  color: white;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-family: 'Cairo', sans-serif;
}

.welcome-footer {
  grid-area: footer;
  text-align: center;
  padding: 8px 15px 12px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 1024px) {
  .welcome-content {
    grid-template-columns: 1fr;
    grid-template-areas:
      "description"
      "actions"
      "features";
    gap: 15px;
    padding: 10px 20px;
  }

  .welcome-description {
    text-align: center;
  }
}

@media (max-width: 768px) {
  .welcome-header {
    padding: 8px 10px 6px;
  }

  .welcome-logo h1 {
    font-size: 20px;
  }

  .logo-icon {
    font-size: 1.2rem;
  }

  .welcome-content {
    padding: 8px 10px;
    gap: 12px;
  }

  .welcome-description h2 {
    font-size: 16px;
  }

  .welcome-description p {
    font-size: 11px;
  }

  .welcome-image {
    max-height: 120px;
  }

  .quick-actions h3 {
    font-size: 14px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    padding: 8px 12px;
    min-width: auto;
    max-width: none;
  }

  .btn-title {
    font-size: 12px;
  }

  .btn-desc {
    font-size: 9px;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 6px;
  }

  .feature-item {
    padding: 6px 8px;
    flex-direction: column;
    text-align: center;
    gap: 4px;
  }

  .feature-icon {
    font-size: 12px;
  }

  .feature-text {
    font-size: 9px;
  }
}
