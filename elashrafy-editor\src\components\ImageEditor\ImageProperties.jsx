import React from 'react';
import './ImageProperties.css';

const ImageProperties = ({
  transformations,
  onTransformationsChange,
  onApplyTransformations
}) => {
  const handleTransformationChange = (property, value) => {
    const newTransformations = {
      ...transformations,
      [property]: value
    };
    onTransformationsChange(newTransformations);

    // تطبيق التحويلات فوراً للمعاينة
    setTimeout(() => {
      onApplyTransformations();
    }, 100);
  };

  const resetTransformations = () => {
    onTransformationsChange({
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
      brightness: 0,
      contrast: 0,
      saturation: 0,
      hue: 0
    });
  };

  return (
    <div className="image-properties">
      <div className="properties-header">
        <h3>خصائص الصورة</h3>
        <button 
          className="reset-button"
          onClick={resetTransformations}
          title="إعادة تعيين جميع التحويلات"
        >
          🔄 إعادة تعيين
        </button>
      </div>

      <div className="properties-content">
        
        {/* التدوير والقلب */}
        <div className="property-section">
          <h4>التدوير والقلب</h4>
          
          <div className="property-group">
            <label>التدوير:</label>
            <div className="rotation-controls">
              <input
                type="range"
                min="-180"
                max="180"
                value={transformations.rotation}
                onChange={(e) => handleTransformationChange('rotation', parseInt(e.target.value))}
                className="range-input"
              />
              <span className="value-display">{transformations.rotation}°</span>
            </div>
            <div className="quick-rotation">
              <button onClick={() => handleTransformationChange('rotation', transformations.rotation - 90)}>
                ↶ -90°
              </button>
              <button onClick={() => handleTransformationChange('rotation', transformations.rotation + 90)}>
                ↷ +90°
              </button>
            </div>
          </div>

          <div className="property-group">
            <label>القلب:</label>
            <div className="flip-controls">
              <button 
                className={`flip-button ${transformations.flipX ? 'active' : ''}`}
                onClick={() => handleTransformationChange('flipX', !transformations.flipX)}
              >
                ↔️ أفقي
              </button>
              <button 
                className={`flip-button ${transformations.flipY ? 'active' : ''}`}
                onClick={() => handleTransformationChange('flipY', !transformations.flipY)}
              >
                ↕️ عمودي
              </button>
            </div>
          </div>
        </div>

        {/* تغيير الحجم */}
        <div className="property-section">
          <h4>تغيير الحجم</h4>
          
          <div className="property-group">
            <label>العرض:</label>
            <div className="scale-control">
              <input
                type="range"
                min="0.1"
                max="3"
                step="0.1"
                value={transformations.scaleX}
                onChange={(e) => handleTransformationChange('scaleX', parseFloat(e.target.value))}
                className="range-input"
              />
              <span className="value-display">{Math.round(transformations.scaleX * 100)}%</span>
            </div>
          </div>

          <div className="property-group">
            <label>الارتفاع:</label>
            <div className="scale-control">
              <input
                type="range"
                min="0.1"
                max="3"
                step="0.1"
                value={transformations.scaleY}
                onChange={(e) => handleTransformationChange('scaleY', parseFloat(e.target.value))}
                className="range-input"
              />
              <span className="value-display">{Math.round(transformations.scaleY * 100)}%</span>
            </div>
          </div>

          <div className="property-group">
            <button 
              className="link-button"
              onClick={() => {
                const avgScale = (transformations.scaleX + transformations.scaleY) / 2;
                handleTransformationChange('scaleX', avgScale);
                handleTransformationChange('scaleY', avgScale);
              }}
            >
              🔗 ربط النسب
            </button>
          </div>
        </div>

        {/* تعديل الألوان */}
        <div className="property-section">
          <h4>تعديل الألوان</h4>
          
          <div className="property-group">
            <label>السطوع:</label>
            <div className="color-control">
              <input
                type="range"
                min="-100"
                max="100"
                value={transformations.brightness}
                onChange={(e) => handleTransformationChange('brightness', parseInt(e.target.value))}
                className="range-input"
              />
              <span className="value-display">{transformations.brightness}</span>
            </div>
          </div>

          <div className="property-group">
            <label>التباين:</label>
            <div className="color-control">
              <input
                type="range"
                min="-100"
                max="100"
                value={transformations.contrast}
                onChange={(e) => handleTransformationChange('contrast', parseInt(e.target.value))}
                className="range-input"
              />
              <span className="value-display">{transformations.contrast}</span>
            </div>
          </div>

          <div className="property-group">
            <label>التشبع:</label>
            <div className="color-control">
              <input
                type="range"
                min="-100"
                max="100"
                value={transformations.saturation}
                onChange={(e) => handleTransformationChange('saturation', parseInt(e.target.value))}
                className="range-input"
              />
              <span className="value-display">{transformations.saturation}</span>
            </div>
          </div>

          <div className="property-group">
            <label>درجة اللون:</label>
            <div className="color-control">
              <input
                type="range"
                min="-180"
                max="180"
                value={transformations.hue}
                onChange={(e) => handleTransformationChange('hue', parseInt(e.target.value))}
                className="range-input"
              />
              <span className="value-display">{transformations.hue}°</span>
            </div>
          </div>
        </div>

        {/* تطبيق التغييرات */}
        <div className="property-section">
          <button 
            className="apply-button"
            onClick={onApplyTransformations}
          >
            ✅ تطبيق التغييرات
          </button>
        </div>

      </div>
    </div>
  );
};

export default ImageProperties;
