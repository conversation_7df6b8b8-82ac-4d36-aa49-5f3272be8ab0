import React from 'react';
import './ProcessingOverlay.css';

const ProcessingOverlay = ({ 
  isVisible, 
  progress, 
  message, 
  onCancel,
  canCancel = true 
}) => {
  if (!isVisible) return null;

  return (
    <div className="processing-overlay">
      <div className="processing-modal">
        <div className="processing-content">
          {/* أيقونة التحميل */}
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
          
          {/* رسالة المعالجة */}
          <div className="processing-message">
            {message || 'جاري المعالجة...'}
          </div>
          
          {/* شريط التقدم */}
          <div className="progress-container">
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <div className="progress-text">
              {Math.round(progress)}%
            </div>
          </div>
          
          {/* زر الإلغاء */}
          {canCancel && onCancel && (
            <button 
              className="cancel-btn"
              onClick={onCancel}
              title="إلغاء المعالجة"
            >
              إلغاء
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProcessingOverlay;
