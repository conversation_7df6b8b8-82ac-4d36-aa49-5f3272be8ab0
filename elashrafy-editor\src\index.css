:root {
  font-family: 'Cairo', 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Noto Sans Arabic', 'Traditional Arabic', Geneva, Verdana, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: #333;
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  direction: rtl;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

#root {
  height: 100vh;
  overflow: hidden;
}

button {
  border-radius: 4px;
  border: 1px solid #ddd;
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 400;
  font-family: inherit;
  background-color: #ffffff;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

button:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
}

button:focus,
button:focus-visible {
  outline: 2px solid #007acc;
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

input, textarea {
  font-family: inherit;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  gap: 8px;
}

.btn-primary {
  background-color: var(--primary-blue);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-blue-dark);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
}

.btn-icon {
  padding: 8px;
  min-width: 36px;
  min-height: 36px;
}

/* Input Styles */
.input {
  padding: 8px 12px;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: 14px;
  background-color: var(--white);
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Card Styles */
.card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
}

/* Utility Classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.p-2 {
  padding: 8px;
}

.p-4 {
  padding: 16px;
}

.m-2 {
  margin: 8px;
}

.m-4 {
  margin: 16px;
}

.text-sm {
  font-size: 14px;
}

.text-lg {
  font-size: 18px;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.text-gray-600 {
  color: var(--gray-600);
}

.text-primary {
  color: var(--primary-blue);
}

.bg-white {
  background-color: var(--white);
}

.border {
  border: 1px solid var(--gray-200);
}

.rounded {
  border-radius: var(--border-radius);
}

.shadow {
  box-shadow: var(--shadow);
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  user-select: none;
}

.overflow-hidden {
  overflow: hidden;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}
