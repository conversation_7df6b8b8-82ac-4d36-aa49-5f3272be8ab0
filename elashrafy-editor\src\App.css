.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

.app-header {
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
  color: white;
  padding: 8px 16px;
  border-bottom: 3px solid #666666;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left,
.header-right {
  flex: 1;
  display: flex;
  align-items: center;
}

.header-right {
  justify-content: flex-end;
}

/* زر العودة للصفحة الرئيسية */
.back-to-welcome-btn {
  background: linear-gradient(45deg, #4a90e2, #5ba0f2);
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.3);
}

.back-to-welcome-btn:hover {
  background: linear-gradient(45deg, #5ba0f2, #6bb0ff);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.4);
}

.back-to-welcome-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.3);
}

.app-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.app-logo {
  font-size: 20px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* تحسينات عامة */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
}

/* شريط التمرير المخصص */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .app-header {
    padding: 6px 12px;
  }

  .app-title {
    font-size: 14px;
  }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1e1e1e;
    color: #d4d4d4;
  }

  .app-header {
    background-color: #252526;
    border-color: #3e3e42;
  }

  ::-webkit-scrollbar-track {
    background: #2d2d30;
  }

  ::-webkit-scrollbar-thumb {
    background: #424242;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #4f4f4f;
  }
}
