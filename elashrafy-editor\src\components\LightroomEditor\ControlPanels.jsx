import { useState } from 'react';
import BasicPanel from './panels/BasicPanel';
import ColorPanel from './panels/ColorPanel';
import HSLPanel from './panels/HSLPanel';
import DetailPanel from './panels/DetailPanel';
import TransformPanel from './panels/TransformPanel';
import './ControlPanels.css';

const ControlPanels = ({
  activePanel,
  onPanelChange,
  adjustments,
  onUpdateAdjustment,
  onUpdateHSLAdjustment
}) => {
  const panels = [
    { id: 'basic', name: 'أساسي', icon: '🎚️' },
    { id: 'color', name: 'الألوان', icon: '🎨' },
    { id: 'hsl', name: 'HSL', icon: '🌈' },
    { id: 'detail', name: 'التفاصيل', icon: '🔍' },
    { id: 'transform', name: 'التحويل', icon: '🔄' }
  ];

  const renderActivePanel = () => {
    switch (activePanel) {
      case 'basic':
        return (
          <BasicPanel
            adjustments={adjustments}
            onUpdateAdjustment={onUpdateAdjustment}
          />
        );
      case 'color':
        return (
          <ColorPanel
            adjustments={adjustments}
            onUpdateAdjustment={onUpdateAdjustment}
          />
        );
      case 'hsl':
        return (
          <HSLPanel
            adjustments={adjustments}
            onUpdateHSLAdjustment={onUpdateHSLAdjustment}
          />
        );
      case 'detail':
        return (
          <DetailPanel
            adjustments={adjustments}
            onUpdateAdjustment={onUpdateAdjustment}
          />
        );
      case 'transform':
        return (
          <TransformPanel
            adjustments={adjustments}
            onUpdateAdjustment={onUpdateAdjustment}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="control-panels">
      {/* تبويبات اللوحات */}
      <div className="panel-tabs">
        {panels.map(panel => (
          <button
            key={panel.id}
            className={`panel-tab ${activePanel === panel.id ? 'active' : ''}`}
            onClick={() => onPanelChange(panel.id)}
            title={panel.name}
          >
            <span className="tab-icon">{panel.icon}</span>
            <span className="tab-name">{panel.name}</span>
          </button>
        ))}
      </div>

      {/* محتوى اللوحة النشطة */}
      <div className="panel-content">
        {renderActivePanel()}
      </div>
    </div>
  );
};

export default ControlPanels;
