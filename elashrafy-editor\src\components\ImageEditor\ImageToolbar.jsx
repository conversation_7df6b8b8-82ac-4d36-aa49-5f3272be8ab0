import React from 'react';
import './ImageToolbar.css';

const ImageToolbar = ({
  selectedTool,
  onToolSelect,
  toolSettings,
  onToolSettingsChange,
  onUndo,
  onRedo,
  canUndo,
  canRedo
}) => {
  const tools = [
    { id: 'select', name: 'تحديد', icon: '👆', description: 'أداة التحديد والتحريك' },
    { id: 'crop', name: 'قص', icon: '✂️', description: 'قص الصورة' },
    { id: 'brush', name: 'فرشاة', icon: '🖌️', description: 'الرسم بالفرشاة' },
    { id: 'eraser', name: 'ممحاة', icon: '🧽', description: 'مسح أجزاء من الصورة' },
    { id: 'text', name: 'نص', icon: '📝', description: 'إضافة نص' },
    { id: 'rectangle', name: 'مستطيل', icon: '⬜', description: 'رسم مستطيل' },
    { id: 'circle', name: 'دائرة', icon: '⭕', description: 'رسم دائرة' },
    { id: 'arrow', name: 'سهم', icon: '➡️', description: 'رسم سهم' },
    { id: 'line', name: 'خط', icon: '📏', description: 'رسم خط مستقيم' },
    { id: 'highlighter', name: 'تمييز', icon: '🖍️', description: 'أداة التمييز' }
  ];

  const handleToolSettingChange = (setting, value) => {
    const newSettings = {
      ...toolSettings,
      [setting]: value
    };
    onToolSettingsChange(newSettings);
  };

  return (
    <div className="image-toolbar">
      {/* أدوات التراجع والإعادة */}
      <div className="toolbar-section">
        <div className="section-title">التحكم</div>
        <div className="toolbar-group">
          <button
            className={`tool-button ${!canUndo ? 'disabled' : ''}`}
            onClick={onUndo}
            disabled={!canUndo}
            title="تراجع (Ctrl+Z)"
          >
            <span className="tool-icon">↶</span>
            <span className="tool-name">تراجع</span>
          </button>
          <button
            className={`tool-button ${!canRedo ? 'disabled' : ''}`}
            onClick={onRedo}
            disabled={!canRedo}
            title="إعادة (Ctrl+Y)"
          >
            <span className="tool-icon">↷</span>
            <span className="tool-name">إعادة</span>
          </button>
        </div>
      </div>

      {/* الأدوات الرئيسية */}
      <div className="toolbar-section">
        <div className="section-title">الأدوات</div>
        <div className="toolbar-group tools-grid">
          {tools.map(tool => (
            <button
              key={tool.id}
              className={`tool-button ${selectedTool === tool.id ? 'active' : ''}`}
              onClick={() => {
                console.log('تم اختيار الأداة:', tool.id);
                onToolSelect(tool.id);
              }}
              title={tool.description}
            >
              <span className="tool-icon">{tool.icon}</span>
              <span className="tool-name">{tool.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* إعدادات الأدوات */}
      <div className="toolbar-section">
        <div className="section-title">الإعدادات</div>
        <div className="toolbar-settings">
          
          {/* إعدادات الفرشاة */}
          {(selectedTool === 'brush' || selectedTool === 'eraser' || selectedTool === 'highlighter') && (
            <div className="setting-group">
              <label>حجم الفرشاة:</label>
              <input
                type="range"
                min="1"
                max="50"
                value={toolSettings.brushSize}
                onChange={(e) => handleToolSettingChange('brushSize', parseInt(e.target.value))}
                className="range-input"
              />
              <span className="setting-value">{toolSettings.brushSize}px</span>
            </div>
          )}

          {/* إعدادات اللون */}
          {(selectedTool === 'brush' || selectedTool === 'text' || selectedTool === 'rectangle' || 
            selectedTool === 'circle' || selectedTool === 'arrow' || selectedTool === 'line') && (
            <div className="setting-group">
              <label>اللون:</label>
              <input
                type="color"
                value={toolSettings.brushColor}
                onChange={(e) => handleToolSettingChange('brushColor', e.target.value)}
                className="color-input"
              />
            </div>
          )}

          {/* إعدادات النص */}
          {selectedTool === 'text' && (
            <>
              <div className="setting-group">
                <label>حجم الخط:</label>
                <input
                  type="range"
                  min="8"
                  max="72"
                  value={toolSettings.textSize}
                  onChange={(e) => handleToolSettingChange('textSize', parseInt(e.target.value))}
                  className="range-input"
                />
                <span className="setting-value">{toolSettings.textSize}px</span>
              </div>
              <div className="setting-group">
                <label>نوع الخط:</label>
                <select
                  value={toolSettings.textFont}
                  onChange={(e) => handleToolSettingChange('textFont', e.target.value)}
                  className="select-input"
                >
                  <option value="Arial">Arial</option>
                  <option value="Helvetica">Helvetica</option>
                  <option value="Times New Roman">Times New Roman</option>
                  <option value="Courier New">Courier New</option>
                  <option value="Tahoma">Tahoma</option>
                  <option value="Verdana">Verdana</option>
                </select>
              </div>
            </>
          )}

          {/* إعدادات الأشكال */}
          {(selectedTool === 'rectangle' || selectedTool === 'circle' || selectedTool === 'arrow' || selectedTool === 'line') && (
            <>
              <div className="setting-group">
                <label>سمك الحد:</label>
                <input
                  type="range"
                  min="1"
                  max="20"
                  value={toolSettings.shapeStroke}
                  onChange={(e) => handleToolSettingChange('shapeStroke', parseInt(e.target.value))}
                  className="range-input"
                />
                <span className="setting-value">{toolSettings.shapeStroke}px</span>
              </div>
              <div className="setting-group">
                <label>
                  <input
                    type="checkbox"
                    checked={toolSettings.shapeFill}
                    onChange={(e) => handleToolSettingChange('shapeFill', e.target.checked)}
                  />
                  تعبئة الشكل
                </label>
              </div>
            </>
          )}

        </div>
      </div>
    </div>
  );
};

export default ImageToolbar;
