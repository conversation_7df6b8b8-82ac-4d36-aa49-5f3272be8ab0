.text-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.editor-info {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 4px 12px;
  font-size: 12px;
  color: #6c757d;
  direction: rtl;
}

.line-count {
  font-family: 'Courier New', monospace;
}

.text-editor {
  flex: 1;
  border: none;
  outline: none;
  padding: 16px;
  font-family: 'Courier New', 'Consolas', 'Monaco', 'Tahoma', 'Arial Unicode MS', monospace;
  font-size: 14px;
  line-height: 1.6;
  resize: none;
  background-color: #ffffff;
  color: #333;
  direction: auto;
  text-align: start;
  unicode-bidi: plaintext;
  word-wrap: break-word;
  overflow-wrap: break-word;
  writing-mode: horizontal-tb;
}

.text-editor:focus {
  background-color: #fefefe;
}

.text-editor::placeholder {
  color: #999;
  font-style: italic;
}

/* تمييز النص المحدد */
.text-editor::selection {
  background-color: #007acc;
  color: white;
}

.text-editor::-moz-selection {
  background-color: #007acc;
  color: white;
}

/* طبقة تمييز البحث */
.search-highlight-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.highlight-content {
  padding: 16px;
  font-family: 'Courier New', 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: transparent;
  direction: auto;
}

.highlight-content mark {
  background-color: #ffeb3b;
  color: transparent;
  padding: 0;
  border-radius: 2px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .text-editor {
    padding: 12px;
    font-size: 16px; /* منع التكبير التلقائي في iOS */
  }
  
  .editor-info {
    padding: 6px 12px;
    font-size: 11px;
  }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .text-editor {
    background-color: #1e1e1e;
    color: #d4d4d4;
  }
  
  .text-editor:focus {
    background-color: #252526;
  }
  
  .editor-info {
    background-color: #2d2d30;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .text-editor::placeholder {
    color: #6a6a6a;
  }
}
