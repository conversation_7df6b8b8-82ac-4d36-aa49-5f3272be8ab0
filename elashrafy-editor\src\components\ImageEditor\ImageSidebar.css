.image-sidebar {
  width: 350px;
  min-width: 350px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
  border-left: 2px solid #cccccc;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: 100%;
  direction: rtl;
}

.sidebar-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  border-bottom: 2px solid #cccccc;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.sidebar-section {
  border-bottom: 1px solid #e0e0e0;
  transition: all 0.3s ease;
}

.sidebar-section.active {
  background: linear-gradient(135deg, #f9f9f9 0%, #f0f0f0 100%);
  border-left: 4px solid #666666;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  background: linear-gradient(135deg, #f8f8f8 0%, #f0f0f0 100%);
  border-bottom: 1px solid #e0e0e0;
  transition: all 0.2s ease;
  user-select: none;
}

.section-header:hover {
  background: linear-gradient(135deg, #f0f0f0 0%, #e8e8e8 100%);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-icon {
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.collapse-icon {
  font-size: 14px;
  color: #666;
  transition: transform 0.3s ease;
  transform-origin: center;
}

.collapse-icon.collapsed {
  transform: rotate(-90deg);
}

.section-content {
  max-height: 500px;
  overflow-y: auto;
  transition: all 0.3s ease;
  opacity: 1;
}

.section-content.collapsed {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  padding: 0;
}

.sidebar-footer {
  display: flex;
  justify-content: center;
  gap: 10px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  border-top: 2px solid #cccccc;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
}

.quick-btn {
  width: 50px;
  height: 50px;
  border: 2px solid #cccccc;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
  cursor: pointer;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.quick-btn:hover {
  background: linear-gradient(135deg, #f0f0f0 0%, #e8e8e8 100%);
  border-color: #999999;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.quick-btn.active {
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
  border-color: #666666;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 1024px) {
  .image-sidebar {
    width: 300px;
    min-width: 300px;
  }
  
  .sidebar-header {
    padding: 12px 16px;
  }
  
  .sidebar-header h3 {
    font-size: 16px;
  }
  
  .section-header {
    padding: 12px 16px;
  }
  
  .section-title {
    font-size: 14px;
  }
  
  .section-icon {
    font-size: 18px;
    width: 20px;
    height: 20px;
  }
  
  .sidebar-footer {
    padding: 12px 16px;
  }
  
  .quick-btn {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .image-sidebar {
    width: 100%;
    min-width: auto;
    height: 250px;
    border-left: none;
    border-top: 2px solid #cccccc;
  }
  
  .sidebar-content {
    max-height: 150px;
  }
  
  .section-content {
    max-height: 200px;
  }
  
  .sidebar-footer {
    flex-direction: row;
    justify-content: space-around;
  }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .image-sidebar {
    background: linear-gradient(135deg, #2d2d30 0%, #252526 100%);
    border-color: #3e3e42;
  }
  
  .sidebar-header {
    background: linear-gradient(135deg, #383838 0%, #2d2d30 100%);
    border-color: #3e3e42;
  }
  
  .sidebar-header h3 {
    color: #cccccc;
  }
  
  .sidebar-section.active {
    background: linear-gradient(135deg, #383838 0%, #2d2d30 100%);
    border-color: #cccccc;
  }
  
  .section-header {
    background: linear-gradient(135deg, #383838 0%, #2d2d30 100%);
    border-color: #3e3e42;
  }
  
  .section-header:hover {
    background: linear-gradient(135deg, #3e3e42 0%, #383838 100%);
  }
  
  .section-title {
    color: #cccccc;
  }
  
  .collapse-icon {
    color: #adb5bd;
  }
  
  .sidebar-footer {
    background: linear-gradient(135deg, #383838 0%, #2d2d30 100%);
    border-color: #3e3e42;
  }
  
  .quick-btn {
    background: linear-gradient(135deg, #383838 0%, #2d2d30 100%);
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .quick-btn:hover {
    background: linear-gradient(135deg, #3e3e42 0%, #383838 100%);
    border-color: #6c757d;
  }
}
