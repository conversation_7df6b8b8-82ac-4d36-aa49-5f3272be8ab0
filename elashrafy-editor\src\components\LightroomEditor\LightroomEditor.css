.lightroom-editor {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #1e1e1e;
  color: #ffffff;
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  direction: rtl;
  touch-action: none; /* منع التكبير باللمس */
  user-select: none; /* منع تحديد النص */
  overflow: hidden; /* منع التمرير */
}

.editor-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  direction: rtl;
}

.image-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #2a2a2a;
  position: relative;
  overflow: hidden;
  direction: ltr; /* الصورة تبقى LTR */
}

.side-panels {
  width: 350px;
  background: #1a1a1a;
  border-right: 1px solid #333; /* تغيير من border-left إلى border-right */
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  direction: rtl;
}

/* تحسينات للشرائح */
.slider-container {
  margin: 12px 0;
}

.slider-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #cccccc;
}

.slider-value {
  background: #333;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  min-width: 40px;
  text-align: center;
}

.slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: linear-gradient(to right, #333 0%, #4a90e2 50%, #333 100%);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  transition: all 0.2s ease;
}

.slider:hover {
  background: linear-gradient(to right, #444 0%, #5ba0f2 50%, #444 100%);
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  border: 3px solid #ffffff;
  box-shadow: 0 3px 6px rgba(74, 144, 226, 0.4);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  border: 3px solid #ffffff;
  box-shadow: 0 3px 6px rgba(74, 144, 226, 0.4);
  transition: all 0.2s ease;
}

.slider:hover::-webkit-slider-thumb {
  background: #5ba0f2;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.6);
}

.slider:hover::-moz-range-thumb {
  background: #5ba0f2;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.6);
}

.slider:active::-webkit-slider-thumb {
  transform: scale(1.2);
  box-shadow: 0 5px 10px rgba(74, 144, 226, 0.8);
}

.slider:active::-moz-range-thumb {
  transform: scale(1.2);
  box-shadow: 0 5px 10px rgba(74, 144, 226, 0.8);
}

/* مؤشر المعالجة الصغير */
.mini-processing-indicator {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
}

.mini-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(74, 144, 226, 0.3);
  border-top: 2px solid #4a90e2;
  border-radius: 50%;
  animation: miniSpin 0.5s linear infinite;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

@keyframes miniSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تحسينات للأزرار */
.btn {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-family: inherit;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn:hover {
  background: #5ba0f2;
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  background: #555;
  color: #999;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: #555;
  color: #ccc;
}

.btn-secondary:hover {
  background: #666;
}

.btn-danger {
  background: #e74c3c;
}

.btn-danger:hover {
  background: #c0392b;
}

.btn-small {
  padding: 4px 8px;
  font-size: 11px;
}

/* تحسينات للوحات */
.panel {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.panel-header {
  background: #2a2a2a;
  padding: 12px 16px;
  border-bottom: 1px solid #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.panel-title {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.panel-content {
  padding: 16px;
}

.panel-toggle {
  background: none;
  border: none;
  color: #ccc;
  font-size: 16px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.panel-toggle.collapsed {
  transform: rotate(-90deg);
}

/* تحسينات للتبويبات */
.tabs {
  display: flex;
  background: #2a2a2a;
  border-bottom: 1px solid #333;
}

.tab {
  flex: 1;
  padding: 12px 16px;
  background: none;
  border: none;
  color: #ccc;
  cursor: pointer;
  font-size: 13px;
  font-family: inherit;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab:hover {
  background: #333;
  color: #fff;
}

.tab.active {
  color: #4a90e2;
  border-bottom-color: #4a90e2;
  background: #333;
}

/* تحسينات للمدخلات */
.input-group {
  margin-bottom: 16px;
}

.input-label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  color: #cccccc;
  font-weight: 500;
}

.input {
  width: 100%;
  padding: 8px 12px;
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #fff;
  font-size: 13px;
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.select {
  width: 100%;
  padding: 8px 12px;
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #fff;
  font-size: 13px;
  font-family: inherit;
  cursor: pointer;
}

.select:focus {
  outline: none;
  border-color: #4a90e2;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 1024px) {
  .side-panels {
    width: 300px;
  }
  
  .panel-content {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .editor-content {
    flex-direction: column;
  }
  
  .side-panels {
    width: 100%;
    height: 40vh;
    border-left: none;
    border-top: 1px solid #333;
  }
  
  .image-area {
    height: 60vh;
  }
}

/* تحسينات للحركة */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
  .lightroom-editor {
    background: #000000;
  }
  
  .panel {
    border-color: #666;
  }
  
  .slider {
    background: #666;
  }
  
  .btn {
    border: 1px solid #fff;
  }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
