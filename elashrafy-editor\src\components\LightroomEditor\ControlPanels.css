.control-panels {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1a1a1a;
}

/* تبويبات اللوحات */
.panel-tabs {
  display: flex;
  background: #2a2a2a;
  border-bottom: 1px solid #333;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.panel-tabs::-webkit-scrollbar {
  display: none;
}

.panel-tab {
  flex: 1;
  min-width: 60px;
  padding: 12px 8px;
  background: none;
  border: none;
  color: #cccccc;
  cursor: pointer;
  font-size: 11px;
  font-family: inherit;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.panel-tab:hover {
  background: #333;
  color: #ffffff;
}

.panel-tab.active {
  color: #4a90e2;
  border-bottom-color: #4a90e2;
  background: #333;
}

.tab-icon {
  font-size: 16px;
}

.tab-name {
  font-weight: 500;
  white-space: nowrap;
}

/* محتوى اللوحة */
.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* مكونات مشتركة للوحات */
.panel-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #333;
}

.control-group {
  margin-bottom: 16px;
}

.control-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #cccccc;
}

.control-name {
  font-weight: 500;
}

.control-value {
  background: #333;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  min-width: 40px;
  text-align: center;
  color: #ffffff;
}

.control-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: linear-gradient(to right, #333 0%, #4a90e2 50%, #333 100%);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  position: relative;
  transition: all 0.2s ease;
}

.control-slider:hover {
  background: linear-gradient(to right, #444 0%, #5ba0f2 50%, #444 100%);
}

.control-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  border: 3px solid #ffffff;
  box-shadow: 0 3px 6px rgba(74, 144, 226, 0.4);
  transition: all 0.2s ease;
}

.control-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  border: 3px solid #ffffff;
  box-shadow: 0 3px 6px rgba(74, 144, 226, 0.4);
  transition: all 0.2s ease;
}

.control-slider:hover::-webkit-slider-thumb {
  background: #5ba0f2;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.6);
}

.control-slider:hover::-moz-range-thumb {
  background: #5ba0f2;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.6);
}

.control-slider:active::-webkit-slider-thumb {
  transform: scale(1.2);
  box-shadow: 0 5px 10px rgba(74, 144, 226, 0.8);
}

.control-slider:active::-moz-range-thumb {
  transform: scale(1.2);
  box-shadow: 0 5px 10px rgba(74, 144, 226, 0.8);
}

/* شرائح ملونة للألوان */
.color-slider {
  background: linear-gradient(to right, #ff0000, #ffff00, #00ff00, #00ffff, #0000ff, #ff00ff, #ff0000);
}

.temperature-slider {
  background: linear-gradient(to right, #0066cc, #ffffff, #ffaa00);
}

.tint-slider {
  background: linear-gradient(to right, #00ff00, #ffffff, #ff00ff);
}

/* أزرار إعادة التعيين */
.reset-btn {
  background: none;
  border: 1px solid #555;
  color: #cccccc;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.reset-btn:hover {
  background: #555;
  color: #ffffff;
  border-color: #666;
}

.reset-btn:active {
  background: #666;
}

/* مجموعات الأزرار */
.button-group {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.control-btn {
  flex: 1;
  padding: 8px 12px;
  background: #333;
  border: 1px solid #555;
  color: #cccccc;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-family: inherit;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: #444;
  color: #ffffff;
  border-color: #666;
}

.control-btn:active {
  background: #555;
}

.control-btn.active {
  background: #4a90e2;
  color: #ffffff;
  border-color: #4a90e2;
}

/* مدخلات رقمية */
.number-input {
  width: 60px;
  padding: 4px 8px;
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #ffffff;
  font-size: 12px;
  text-align: center;
}

.number-input:focus {
  outline: none;
  border-color: #4a90e2;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .panel-content {
    padding: 12px;
  }
  
  .panel-section {
    margin-bottom: 20px;
  }
  
  .control-group {
    margin-bottom: 12px;
  }
  
  .tab-name {
    display: none;
  }
  
  .panel-tab {
    min-width: 50px;
    padding: 10px 6px;
  }
  
  .tab-icon {
    font-size: 18px;
  }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1920px) {
  .panel-content {
    padding: 20px;
  }
  
  .control-group {
    margin-bottom: 20px;
  }
  
  .section-title {
    font-size: 16px;
    margin-bottom: 20px;
  }
  
  .control-label {
    font-size: 14px;
  }
  
  .panel-tab {
    padding: 16px 12px;
    font-size: 12px;
  }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
  .panel-tabs {
    border-bottom-color: #ffffff;
  }
  
  .panel-tab {
    border: 1px solid transparent;
  }
  
  .panel-tab:hover,
  .panel-tab.active {
    border-color: #ffffff;
  }
  
  .control-slider {
    border: 1px solid #ffffff;
  }
  
  .control-btn {
    border-color: #ffffff;
  }
}

/* تحسينات إضافية للوحات */
.preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.preset-btn {
  flex: 1;
  min-width: 60px;
  padding: 4px 8px;
  background: #333;
  border: 1px solid #555;
  color: #cccccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.preset-btn:hover {
  background: #444;
  color: #ffffff;
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
  margin-top: 8px;
}

.crop-preset-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 4px;
  background: #333;
  border: 1px solid #555;
  color: #cccccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s ease;
}

.crop-preset-btn:hover {
  background: #444;
  color: #ffffff;
  border-color: #4a90e2;
}

.preset-visual {
  width: 30px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.preset-rect {
  background: #4a90e2;
  border-radius: 2px;
}

.preset-label {
  font-size: 9px;
  text-align: center;
}

.section-subtitle {
  font-size: 11px;
  color: #888;
  margin-bottom: 6px;
  font-weight: 500;
}

.curve-container {
  height: 100px;
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
}

.curve-placeholder {
  text-align: center;
  color: #666;
}

.curve-placeholder span {
  display: block;
  font-size: 12px;
  margin-bottom: 4px;
}

.curve-placeholder small {
  font-size: 10px;
}

.color-balance-controls {
  margin-top: 8px;
}

.balance-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.balance-label {
  font-size: 11px;
  color: #ccc;
  min-width: 50px;
}

.selective-color-controls {
  margin-top: 8px;
}

.color-channel {
  margin-bottom: 16px;
  padding: 8px;
  background: #333;
  border-radius: 4px;
}

.channel-header {
  font-size: 12px;
  font-weight: 600;
  color: #4a90e2;
  margin-bottom: 8px;
}

.channel-controls {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.mini-control {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 10px;
  color: #ccc;
}

.mini-slider {
  flex: 1;
  height: 3px;
  background: #444;
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.mini-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  border: 1px solid #ffffff;
}

.mini-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  border: 1px solid #ffffff;
}

/* تحسينات إمكانية الوصول */
.panel-tab:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

.control-slider:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

.control-btn:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

/* تحسينات لوحة HSL */
.hsl-modes {
  display: flex;
  gap: 4px;
  margin-bottom: 16px;
}

.mode-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 4px;
  background: #333;
  border: 1px solid #555;
  color: #cccccc;
  border-radius: 6px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s ease;
  gap: 4px;
}

.mode-btn:hover {
  background: #444;
  color: #ffffff;
}

.mode-btn.active {
  background: #4a90e2;
  color: #ffffff;
  border-color: #4a90e2;
}

.mode-icon {
  font-size: 16px;
}

.mode-name {
  font-weight: 500;
}

.color-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.color-control-group {
  background: #333;
  padding: 8px;
  border-radius: 6px;
  border: 1px solid #555;
}

.color-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.color-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-swatch {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.color-name {
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
}

.color-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reset-btn.small {
  padding: 2px 6px;
  font-size: 10px;
  min-width: 20px;
  height: 20px;
}

.hue-slider {
  background: linear-gradient(to right,
    #ff0000 0%, #ff8000 12.5%, #ffff00 25%, #80ff00 37.5%,
    #00ff00 50%, #00ff80 62.5%, #00ffff 75%, #0080ff 87.5%, #0000ff 100%);
}

.saturation-slider {
  background: linear-gradient(to right, #808080, #ff0000);
}

.luminance-slider {
  background: linear-gradient(to right, #000000, #808080, #ffffff);
}

.quick-effects {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.effect-btn {
  padding: 8px 12px;
  background: #333;
  border: 1px solid #555;
  color: #cccccc;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
  text-align: center;
}

.effect-btn:hover {
  background: #444;
  color: #ffffff;
  border-color: #4a90e2;
}

.tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tip {
  font-size: 11px;
  color: #888;
  line-height: 1.4;
}

.tip strong {
  color: #4a90e2;
}

/* تحسينات لوحة التفاصيل */
.sharpening-presets,
.noise-presets {
  margin-top: 8px;
}

.info-box {
  background: #2a2a2a;
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
  border-left: 3px solid #4a90e2;
}

.info-box small {
  font-size: 11px;
  color: #ccc;
  line-height: 1.4;
}

.effects-info {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-item {
  font-size: 11px;
  color: #888;
  line-height: 1.4;
}

.info-item strong {
  color: #4a90e2;
}

.quick-settings {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.setting-btn {
  padding: 8px 6px;
  background: #333;
  border: 1px solid #555;
  color: #cccccc;
  border-radius: 6px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s ease;
  text-align: center;
  line-height: 1.2;
}

.setting-btn:hover {
  background: #444;
  color: #ffffff;
  border-color: #4a90e2;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.preview-label {
  color: #888;
}

.preview-value {
  color: #4a90e2;
  font-weight: 500;
}

/* تحسينات لوحة التحويل */
.rotation-controls {
  margin-top: 8px;
}

.rotation-buttons {
  display: flex;
  gap: 6px;
  margin-top: 8px;
}

.rotation-btn {
  flex: 1;
  padding: 6px 8px;
  background: #333;
  border: 1px solid #555;
  color: #cccccc;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.rotation-btn:hover {
  background: #444;
  color: #ffffff;
}

.fine-rotation {
  display: flex;
  gap: 6px;
  margin-top: 6px;
}

.fine-btn {
  flex: 1;
  padding: 4px 6px;
  background: #2a2a2a;
  border: 1px solid #444;
  color: #ccc;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s ease;
}

.fine-btn:hover {
  background: #333;
  color: #ffffff;
}

.flip-controls {
  margin-top: 8px;
}

.flip-buttons {
  display: flex;
  gap: 8px;
}

.flip-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: #333;
  border: 1px solid #555;
  color: #cccccc;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 4px;
}

.flip-btn:hover {
  background: #444;
  color: #ffffff;
}

.flip-btn.active {
  background: #4a90e2;
  color: #ffffff;
  border-color: #4a90e2;
}

.flip-icon {
  font-size: 16px;
}

.flip-label {
  font-size: 11px;
  font-weight: 500;
}

.quick-transforms {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.transform-btn {
  padding: 8px 6px;
  background: #333;
  border: 1px solid #555;
  color: #cccccc;
  border-radius: 6px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s ease;
  text-align: center;
  line-height: 1.2;
}

.transform-btn:hover {
  background: #444;
  color: #ffffff;
  border-color: #4a90e2;
}

.transform-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-label {
  color: #888;
  font-size: 11px;
}

.info-value {
  color: #4a90e2;
  font-weight: 500;
  font-size: 11px;
}

.control-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.control-group.small {
  flex: 1;
}

.control-group.small .control-label {
  font-size: 11px;
  margin-bottom: 4px;
}
