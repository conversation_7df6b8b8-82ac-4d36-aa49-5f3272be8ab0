.multi-image-manager {
  width: 300px;
  min-width: 300px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
  border-right: 2px solid #cccccc;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: 100%;
  direction: rtl;
  font-family: 'Cairo', sans-serif;
}

.image-toolbar {
  padding: 15px;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  border-bottom: 2px solid #cccccc;
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.add-image-btn, .export-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: 'Cairo', sans-serif;
}

.add-image-btn:hover, .export-btn:hover {
  background: linear-gradient(135deg, #e8e8e8 0%, #d8d8d8 100%);
  border-color: #999;
  transform: translateY(-1px);
}

.export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 14px;
}

.image-counter {
  margin-right: auto;
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

.images-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.image-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.5);
}

.image-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: #ddd;
}

.image-item.active {
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
  border-color: #666666;
  color: white;
}

.image-preview {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
  background: #f0f0f0;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-info {
  flex: 1;
  min-width: 0;
}

.image-name {
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.image-details {
  font-size: 10px;
  opacity: 0.7;
}

.remove-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 0, 0, 0.1);
  color: #ff4444;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.remove-btn:hover {
  background: rgba(255, 0, 0, 0.2);
  transform: scale(1.1);
}

.export-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.export-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  direction: rtl;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  font-family: 'Cairo', sans-serif;
}

.close-btn {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  background: #f0f0f0;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e0e0e0;
}

.dialog-content {
  padding: 20px;
}

.setting-group {
  margin-bottom: 20px;
}

.setting-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  font-family: 'Cairo', sans-serif;
}

.setting-group select,
.setting-group input[type="range"],
.setting-group input[type="number"] {
  width: 100%;
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  font-family: 'Cairo', sans-serif;
}

.dimensions-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.dimensions-inputs input {
  flex: 1;
}

.dimensions-inputs span {
  font-size: 16px;
  font-weight: bold;
  color: #666;
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  font-size: 13px !important;
  margin-bottom: 0 !important;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto !important;
  margin: 0;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;
}

.cancel-btn, .export-btn.primary {
  padding: 10px 20px;
  border: 2px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: 'Cairo', sans-serif;
}

.cancel-btn {
  background: #f8f8f8;
  color: #666;
}

.cancel-btn:hover {
  background: #e8e8e8;
  border-color: #999;
}

.export-btn.primary {
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
  color: white;
  border-color: #666666;
}

.export-btn.primary:hover {
  background: linear-gradient(135deg, #444444 0%, #2a2a2a 100%);
  transform: translateY(-1px);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .multi-image-manager {
    width: 100%;
    height: 200px;
    border-right: none;
    border-top: 2px solid #cccccc;
  }
  
  .image-toolbar {
    padding: 10px;
  }
  
  .images-list {
    padding: 8px;
  }
  
  .image-item {
    padding: 8px;
  }
  
  .export-dialog {
    width: 95%;
  }
  
  .dialog-content {
    padding: 15px;
  }
}
