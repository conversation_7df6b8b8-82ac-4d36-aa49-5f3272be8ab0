const BasicPanel = ({ adjustments, onUpdateAdjustment }) => {
  const handleReset = (key) => {
    onUpdateAdjustment(key, 0);
  };

  const handleResetAll = () => {
    onUpdateAdjustment('exposure', 0);
    onUpdateAdjustment('highlights', 0);
    onUpdateAdjustment('shadows', 0);
    onUpdateAdjustment('whites', 0);
    onUpdateAdjustment('blacks', 0);
    onUpdateAdjustment('brightness', 0);
    onUpdateAdjustment('contrast', 0);
  };

  return (
    <div className="basic-panel">
      {/* قسم التعرض */}
      <div className="panel-section">
        <div className="section-title">
          التعرض والإضاءة
          <button 
            className="reset-btn"
            onClick={handleResetAll}
            title="إعادة تعيين جميع قيم التعرض"
          >
            إعادة تعيين
          </button>
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">التعرض</span>
            <span className="control-value">{adjustments.exposure > 0 ? '+' : ''}{adjustments.exposure.toFixed(2)}</span>
          </div>
          <input
            type="range"
            min="-3"
            max="3"
            step="0.01"
            value={adjustments.exposure}
            onChange={(e) => onUpdateAdjustment('exposure', parseFloat(e.target.value))}
            className="control-slider"
          />
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">الإضاءات</span>
            <span className="control-value">{adjustments.highlights > 0 ? '+' : ''}{adjustments.highlights}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.highlights}
            onChange={(e) => onUpdateAdjustment('highlights', parseInt(e.target.value))}
            className="control-slider"
          />
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">الظلال</span>
            <span className="control-value">{adjustments.shadows > 0 ? '+' : ''}{adjustments.shadows}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.shadows}
            onChange={(e) => onUpdateAdjustment('shadows', parseInt(e.target.value))}
            className="control-slider"
          />
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">النقاط البيضاء</span>
            <span className="control-value">{adjustments.whites > 0 ? '+' : ''}{adjustments.whites}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.whites}
            onChange={(e) => onUpdateAdjustment('whites', parseInt(e.target.value))}
            className="control-slider"
          />
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">النقاط السوداء</span>
            <span className="control-value">{adjustments.blacks > 0 ? '+' : ''}{adjustments.blacks}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.blacks}
            onChange={(e) => onUpdateAdjustment('blacks', parseInt(e.target.value))}
            className="control-slider"
          />
        </div>
      </div>

      {/* قسم الألوان الأساسية */}
      <div className="panel-section">
        <div className="section-title">
          الألوان الأساسية
          <button 
            className="reset-btn"
            onClick={() => {
              onUpdateAdjustment('brightness', 0);
              onUpdateAdjustment('contrast', 0);
              onUpdateAdjustment('saturation', 0);
              onUpdateAdjustment('vibrance', 0);
            }}
            title="إعادة تعيين قيم الألوان"
          >
            إعادة تعيين
          </button>
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">السطوع</span>
            <span className="control-value">{adjustments.brightness > 0 ? '+' : ''}{adjustments.brightness}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.brightness}
            onChange={(e) => onUpdateAdjustment('brightness', parseInt(e.target.value))}
            className="control-slider"
          />
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">التباين</span>
            <span className="control-value">{adjustments.contrast > 0 ? '+' : ''}{adjustments.contrast}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.contrast}
            onChange={(e) => onUpdateAdjustment('contrast', parseInt(e.target.value))}
            className="control-slider"
          />
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">التشبع</span>
            <span className="control-value">{adjustments.saturation > 0 ? '+' : ''}{adjustments.saturation}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.saturation}
            onChange={(e) => onUpdateAdjustment('saturation', parseInt(e.target.value))}
            className="control-slider color-slider"
          />
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">الحيوية</span>
            <span className="control-value">{adjustments.vibrance > 0 ? '+' : ''}{adjustments.vibrance}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.vibrance}
            onChange={(e) => onUpdateAdjustment('vibrance', parseInt(e.target.value))}
            className="control-slider color-slider"
          />
        </div>
      </div>

      {/* أزرار سريعة */}
      <div className="panel-section">
        <div className="section-title">إعدادات سريعة</div>
        
        <div className="button-group">
          <button 
            className="control-btn"
            onClick={() => {
              onUpdateAdjustment('exposure', 0.5);
              onUpdateAdjustment('shadows', 30);
              onUpdateAdjustment('vibrance', 20);
            }}
          >
            تفتيح
          </button>
          
          <button 
            className="control-btn"
            onClick={() => {
              onUpdateAdjustment('exposure', -0.3);
              onUpdateAdjustment('contrast', 20);
              onUpdateAdjustment('saturation', 15);
            }}
          >
            تغميق
          </button>
        </div>

        <div className="button-group">
          <button 
            className="control-btn"
            onClick={() => {
              onUpdateAdjustment('contrast', 25);
              onUpdateAdjustment('vibrance', 30);
              onUpdateAdjustment('saturation', 10);
            }}
          >
            حيوي
          </button>
          
          <button 
            className="control-btn"
            onClick={() => {
              onUpdateAdjustment('saturation', -50);
              onUpdateAdjustment('contrast', 10);
            }}
          >
            أبيض وأسود
          </button>
        </div>
      </div>
    </div>
  );
};

export default BasicPanel;
