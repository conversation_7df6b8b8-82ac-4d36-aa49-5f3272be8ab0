const DetailPanel = ({ adjustments, onUpdateAdjustment }) => {
  const handleResetSharpening = () => {
    onUpdateAdjustment('sharpening', 0);
  };

  const handleResetNoise = () => {
    onUpdateAdjustment('noiseReduction', 0);
  };

  const handleResetEffects = () => {
    onUpdateAdjustment('clarity', 0);
    onUpdateAdjustment('dehaze', 0);
  };

  return (
    <div className="detail-panel">
      {/* قسم زيادة الحدة */}
      <div className="panel-section">
        <div className="section-title">
          زيادة الحدة
          <button 
            className="reset-btn"
            onClick={handleResetSharpening}
            title="إعادة تعيين زيادة الحدة"
          >
            إعادة تعيين
          </button>
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">المقدار</span>
            <span className="control-value">{adjustments.sharpening}</span>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            step="1"
            value={adjustments.sharpening}
            onChange={(e) => onUpdateAdjustment('sharpening', parseInt(e.target.value))}
            className="control-slider"
          />
        </div>

        <div className="sharpening-presets">
          <div className="preset-buttons">
            <button 
              className="preset-btn"
              onClick={() => onUpdateAdjustment('sharpening', 25)}
            >
              خفيف
            </button>
            <button 
              className="preset-btn"
              onClick={() => onUpdateAdjustment('sharpening', 50)}
            >
              متوسط
            </button>
            <button 
              className="preset-btn"
              onClick={() => onUpdateAdjustment('sharpening', 75)}
            >
              قوي
            </button>
          </div>
        </div>

        <div className="info-box">
          <small>
            💡 زيادة الحدة تحسن وضوح التفاصيل الدقيقة في الصورة
          </small>
        </div>
      </div>

      {/* قسم إزالة الضوضاء */}
      <div className="panel-section">
        <div className="section-title">
          إزالة الضوضاء
          <button 
            className="reset-btn"
            onClick={handleResetNoise}
            title="إعادة تعيين إزالة الضوضاء"
          >
            إعادة تعيين
          </button>
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">المقدار</span>
            <span className="control-value">{adjustments.noiseReduction}</span>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            step="1"
            value={adjustments.noiseReduction}
            onChange={(e) => onUpdateAdjustment('noiseReduction', parseInt(e.target.value))}
            className="control-slider"
          />
        </div>

        <div className="noise-presets">
          <div className="preset-buttons">
            <button 
              className="preset-btn"
              onClick={() => onUpdateAdjustment('noiseReduction', 20)}
            >
              خفيف
            </button>
            <button 
              className="preset-btn"
              onClick={() => onUpdateAdjustment('noiseReduction', 40)}
            >
              متوسط
            </button>
            <button 
              className="preset-btn"
              onClick={() => onUpdateAdjustment('noiseReduction', 60)}
            >
              قوي
            </button>
          </div>
        </div>

        <div className="info-box">
          <small>
            💡 إزالة الضوضاء تقلل من التشويش في الصور عالية ISO
          </small>
        </div>
      </div>

      {/* قسم التأثيرات */}
      <div className="panel-section">
        <div className="section-title">
          التأثيرات
          <button 
            className="reset-btn"
            onClick={handleResetEffects}
            title="إعادة تعيين التأثيرات"
          >
            إعادة تعيين
          </button>
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">الوضوح</span>
            <span className="control-value">{adjustments.clarity > 0 ? '+' : ''}{adjustments.clarity}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.clarity}
            onChange={(e) => onUpdateAdjustment('clarity', parseInt(e.target.value))}
            className="control-slider"
          />
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">إزالة الضباب</span>
            <span className="control-value">{adjustments.dehaze > 0 ? '+' : ''}{adjustments.dehaze}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.dehaze}
            onChange={(e) => onUpdateAdjustment('dehaze', parseInt(e.target.value))}
            className="control-slider"
          />
        </div>

        <div className="effects-info">
          <div className="info-item">
            <strong>الوضوح:</strong> يزيد التباين في التفاصيل المتوسطة
          </div>
          <div className="info-item">
            <strong>إزالة الضباب:</strong> يحسن الرؤية في الصور الضبابية
          </div>
        </div>
      </div>

      {/* إعدادات سريعة للتفاصيل */}
      <div className="panel-section">
        <div className="section-title">إعدادات سريعة</div>
        
        <div className="quick-settings">
          <button 
            className="setting-btn"
            onClick={() => {
              onUpdateAdjustment('sharpening', 40);
              onUpdateAdjustment('clarity', 20);
              onUpdateAdjustment('noiseReduction', 10);
            }}
          >
            📸 صورة شخصية
          </button>
          
          <button 
            className="setting-btn"
            onClick={() => {
              onUpdateAdjustment('sharpening', 60);
              onUpdateAdjustment('clarity', 30);
              onUpdateAdjustment('dehaze', 15);
            }}
          >
            🏞️ منظر طبيعي
          </button>

          <button 
            className="setting-btn"
            onClick={() => {
              onUpdateAdjustment('sharpening', 80);
              onUpdateAdjustment('clarity', 40);
              onUpdateAdjustment('noiseReduction', 5);
            }}
          >
            🏗️ معمارية
          </button>

          <button 
            className="setting-btn"
            onClick={() => {
              onUpdateAdjustment('sharpening', 30);
              onUpdateAdjustment('clarity', -20);
              onUpdateAdjustment('noiseReduction', 25);
            }}
          >
            🌙 ليلية
          </button>

          <button 
            className="setting-btn"
            onClick={() => {
              onUpdateAdjustment('sharpening', 70);
              onUpdateAdjustment('clarity', 50);
              onUpdateAdjustment('dehaze', 25);
            }}
          >
            🔍 تفاصيل عالية
          </button>

          <button 
            className="setting-btn"
            onClick={() => {
              onUpdateAdjustment('sharpening', 20);
              onUpdateAdjustment('clarity', -30);
              onUpdateAdjustment('noiseReduction', 40);
            }}
          >
            ✨ ناعم
          </button>
        </div>
      </div>

      {/* معاينة التأثيرات */}
      <div className="panel-section">
        <div className="section-title">معاينة</div>
        
        <div className="preview-info">
          <div className="preview-item">
            <span className="preview-label">زيادة الحدة:</span>
            <span className="preview-value">
              {adjustments.sharpening === 0 ? 'معطل' : 
               adjustments.sharpening < 30 ? 'خفيف' :
               adjustments.sharpening < 60 ? 'متوسط' : 'قوي'}
            </span>
          </div>
          
          <div className="preview-item">
            <span className="preview-label">إزالة الضوضاء:</span>
            <span className="preview-value">
              {adjustments.noiseReduction === 0 ? 'معطل' : 
               adjustments.noiseReduction < 30 ? 'خفيف' :
               adjustments.noiseReduction < 60 ? 'متوسط' : 'قوي'}
            </span>
          </div>
          
          <div className="preview-item">
            <span className="preview-label">الوضوح:</span>
            <span className="preview-value">
              {adjustments.clarity === 0 ? 'طبيعي' : 
               adjustments.clarity > 0 ? 'محسن' : 'ناعم'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailPanel;
