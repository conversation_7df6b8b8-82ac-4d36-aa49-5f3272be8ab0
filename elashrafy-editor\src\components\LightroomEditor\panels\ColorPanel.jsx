const ColorPanel = ({ adjustments, onUpdateAdjustment }) => {
  const handleResetWhiteBalance = () => {
    onUpdateAdjustment('temperature', 0);
    onUpdateAdjustment('tint', 0);
  };

  const presetTemperatures = [
    { name: 'ضوء الشمس', value: 0 },
    { name: 'غائم', value: 10 },
    { name: 'ظل', value: 25 },
    { name: 'تنجستن', value: -85 },
    { name: 'فلورسنت', value: -20 },
    { name: 'فلاش', value: 5 }
  ];

  return (
    <div className="color-panel">
      {/* توازن الأبيض */}
      <div className="panel-section">
        <div className="section-title">
          توازن الأبيض
          <button 
            className="reset-btn"
            onClick={handleResetWhiteBalance}
            title="إعادة تعيين توازن الأبيض"
          >
            إعادة تعيين
          </button>
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">درجة الحرارة</span>
            <span className="control-value">{adjustments.temperature > 0 ? '+' : ''}{adjustments.temperature}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.temperature}
            onChange={(e) => onUpdateAdjustment('temperature', parseInt(e.target.value))}
            className="control-slider temperature-slider"
          />
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">الصبغة</span>
            <span className="control-value">{adjustments.tint > 0 ? '+' : ''}{adjustments.tint}</span>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            step="1"
            value={adjustments.tint}
            onChange={(e) => onUpdateAdjustment('tint', parseInt(e.target.value))}
            className="control-slider tint-slider"
          />
        </div>

        {/* إعدادات مسبقة لتوازن الأبيض */}
        <div className="preset-buttons">
          <div className="section-subtitle">إعدادات مسبقة:</div>
          <div className="preset-grid">
            {presetTemperatures.map((preset, index) => (
              <button
                key={index}
                className="preset-btn"
                onClick={() => onUpdateAdjustment('temperature', preset.value)}
                title={`تطبيق إعداد ${preset.name}`}
              >
                {preset.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* منحنيات الألوان */}
      <div className="panel-section">
        <div className="section-title">تصحيح الألوان المتقدم</div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">منحنى RGB</span>
          </div>
          <div className="curve-container">
            <div className="curve-placeholder">
              <span>منحنى الألوان</span>
              <small>قريباً...</small>
            </div>
          </div>
        </div>

        <div className="control-group">
          <div className="control-label">
            <span className="control-name">توازن الألوان</span>
          </div>
          
          <div className="color-balance-controls">
            <div className="balance-row">
              <span className="balance-label">الظلال</span>
              <input
                type="range"
                min="-100"
                max="100"
                step="1"
                defaultValue="0"
                className="control-slider"
                onChange={() => {}}
              />
            </div>
            
            <div className="balance-row">
              <span className="balance-label">الوسط</span>
              <input
                type="range"
                min="-100"
                max="100"
                step="1"
                defaultValue="0"
                className="control-slider"
                onChange={() => {}}
              />
            </div>
            
            <div className="balance-row">
              <span className="balance-label">الإضاءات</span>
              <input
                type="range"
                min="-100"
                max="100"
                step="1"
                defaultValue="0"
                className="control-slider"
                onChange={() => {}}
              />
            </div>
          </div>
        </div>
      </div>

      {/* تصحيح الألوان الانتقائي */}
      <div className="panel-section">
        <div className="section-title">تصحيح الألوان الانتقائي</div>

        <div className="selective-color-controls">
          <div className="color-channel">
            <div className="channel-header">الأحمر</div>
            <div className="channel-controls">
              <div className="mini-control">
                <span>سماوي</span>
                <input
                  type="range"
                  min="-100"
                  max="100"
                  defaultValue="0"
                  className="mini-slider"
                  onChange={() => {}}
                />
                <span>أحمر</span>
              </div>
              <div className="mini-control">
                <span>أرجواني</span>
                <input
                  type="range"
                  min="-100"
                  max="100"
                  defaultValue="0"
                  className="mini-slider"
                  onChange={() => {}}
                />
                <span>أصفر</span>
              </div>
              <div className="mini-control">
                <span>أصفر</span>
                <input
                  type="range"
                  min="-100"
                  max="100"
                  defaultValue="0"
                  className="mini-slider"
                  onChange={() => {}}
                />
                <span>أزرق</span>
              </div>
            </div>
          </div>

          <div className="color-channel">
            <div className="channel-header">الأصفر</div>
            <div className="channel-controls">
              <div className="mini-control">
                <span>سماوي</span>
                <input
                  type="range"
                  min="-100"
                  max="100"
                  defaultValue="0"
                  className="mini-slider"
                  onChange={() => {}}
                />
                <span>أحمر</span>
              </div>
              <div className="mini-control">
                <span>أرجواني</span>
                <input
                  type="range"
                  min="-100"
                  max="100"
                  defaultValue="0"
                  className="mini-slider"
                  onChange={() => {}}
                />
                <span>أصفر</span>
              </div>
              <div className="mini-control">
                <span>أصفر</span>
                <input
                  type="range"
                  min="-100"
                  max="100"
                  defaultValue="0"
                  className="mini-slider"
                  onChange={() => {}}
                />
                <span>أزرق</span>
              </div>
            </div>
          </div>

          <div className="color-channel">
            <div className="channel-header">الأخضر</div>
            <div className="channel-controls">
              <div className="mini-control">
                <span>سماوي</span>
                <input
                  type="range"
                  min="-100"
                  max="100"
                  defaultValue="0"
                  className="mini-slider"
                  onChange={() => {}}
                />
                <span>أحمر</span>
              </div>
              <div className="mini-control">
                <span>أرجواني</span>
                <input
                  type="range"
                  min="-100"
                  max="100"
                  defaultValue="0"
                  className="mini-slider"
                  onChange={() => {}}
                />
                <span>أصفر</span>
              </div>
              <div className="mini-control">
                <span>أصفر</span>
                <input
                  type="range"
                  min="-100"
                  max="100"
                  defaultValue="0"
                  className="mini-slider"
                  onChange={() => {}}
                />
                <span>أزرق</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* أدوات سريعة للألوان */}
      <div className="panel-section">
        <div className="section-title">تأثيرات سريعة</div>
        
        <div className="button-group">
          <button 
            className="control-btn"
            onClick={() => {
              onUpdateAdjustment('temperature', 15);
              onUpdateAdjustment('tint', 5);
            }}
          >
            دافئ
          </button>
          
          <button 
            className="control-btn"
            onClick={() => {
              onUpdateAdjustment('temperature', -15);
              onUpdateAdjustment('tint', -5);
            }}
          >
            بارد
          </button>
        </div>

        <div className="button-group">
          <button 
            className="control-btn"
            onClick={() => {
              onUpdateAdjustment('temperature', -30);
              onUpdateAdjustment('tint', 10);
            }}
          >
            سينمائي
          </button>
          
          <button 
            className="control-btn"
            onClick={() => {
              onUpdateAdjustment('temperature', 20);
              onUpdateAdjustment('tint', -10);
            }}
          >
            ذهبي
          </button>
        </div>
      </div>
    </div>
  );
};

export default ColorPanel;
