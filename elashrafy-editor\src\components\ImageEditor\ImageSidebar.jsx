import React, { useState } from 'react';
import ImageProperties from './ImageProperties';
import ImageFilters from './ImageFilters';
import './ImageSidebar.css';

const ImageSidebar = ({
  transformations,
  onTransformationsChange,
  onApplyTransformations,
  onFilterApply
}) => {
  const [activeSection, setActiveSection] = useState('properties');
  const [collapsedSections, setCollapsedSections] = useState({
    properties: false,
    filters: false
  });

  const toggleSection = (section) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const setActiveAndExpand = (section) => {
    setActiveSection(section);
    setCollapsedSections(prev => ({
      ...prev,
      [section]: false
    }));
  };

  return (
    <div className="image-sidebar">
      <div className="sidebar-header">
        <h3>أدوات التحرير</h3>
      </div>

      <div className="sidebar-content">
        {/* قسم خصائص الصورة */}
        <div className={`sidebar-section ${activeSection === 'properties' ? 'active' : ''}`}>
          <div 
            className="section-header"
            onClick={() => {
              toggleSection('properties');
              setActiveAndExpand('properties');
            }}
          >
            <div className="section-title">
              <span className="section-icon">🔧</span>
              <span>خصائص الصورة</span>
            </div>
            <span className={`collapse-icon ${collapsedSections.properties ? 'collapsed' : ''}`}>
              ▼
            </span>
          </div>
          
          <div className={`section-content ${collapsedSections.properties ? 'collapsed' : ''}`}>
            <ImageProperties
              transformations={transformations}
              onTransformationsChange={onTransformationsChange}
              onApplyTransformations={onApplyTransformations}
            />
          </div>
        </div>

        {/* قسم الفلاتر والتأثيرات */}
        <div className={`sidebar-section ${activeSection === 'filters' ? 'active' : ''}`}>
          <div 
            className="section-header"
            onClick={() => {
              toggleSection('filters');
              setActiveAndExpand('filters');
            }}
          >
            <div className="section-title">
              <span className="section-icon">🎨</span>
              <span>الفلاتر والتأثيرات</span>
            </div>
            <span className={`collapse-icon ${collapsedSections.filters ? 'collapsed' : ''}`}>
              ▼
            </span>
          </div>
          
          <div className={`section-content ${collapsedSections.filters ? 'collapsed' : ''}`}>
            <ImageFilters
              onFilterApply={onFilterApply}
            />
          </div>
        </div>
      </div>

      {/* أزرار سريعة */}
      <div className="sidebar-footer">
        <button 
          className={`quick-btn ${activeSection === 'properties' ? 'active' : ''}`}
          onClick={() => setActiveAndExpand('properties')}
          title="خصائص الصورة"
        >
          🔧
        </button>
        <button 
          className={`quick-btn ${activeSection === 'filters' ? 'active' : ''}`}
          onClick={() => setActiveAndExpand('filters')}
          title="الفلاتر والتأثيرات"
        >
          🎨
        </button>
      </div>
    </div>
  );
};

export default ImageSidebar;
