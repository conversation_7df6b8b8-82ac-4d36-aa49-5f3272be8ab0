import React from 'react';
import './WelcomeScreen.css';

const WelcomeScreen = ({ onNewFile, onOpenFile, onOpenImageEditor, onClose }) => {
  return (
    <div className="welcome-overlay">
      <button className="close-welcome" onClick={onClose}>✕</button>

      <div className="welcome-header">
        <div className="welcome-logo">
          <span className="logo-icon">📝</span>
          <h1>Elashrafy Editor</h1>
          <p className="logo-subtitle">محرر النصوص والصور المتقدم</p>
        </div>

        <div className="welcome-badge">
          <span className="badge-text">قم بالتجربة الآن مجاناً</span>
          <div className="badge-sparkle">✨</div>
        </div>
      </div>

      <div className="welcome-content">
        <div className="welcome-description">
          <h2>مرحباً بك في محرر الأشرافي</h2>
          <p>
            محرر نصوص وصور متقدم يدعم اللغة العربية بالكامل.
            يمكنك كتابة النصوص وتحرير الصور وتطبيق الفلاتر والتأثيرات البصرية.
            استمتع بتجربة تحرير احترافية مع واجهة عربية متطورة.
          </p>
        </div>

        <div className="quick-actions">
          <h3>البدء السريع</h3>
          <div className="action-buttons">
              <button className="action-btn primary" onClick={onNewFile}>
                <span className="btn-icon">📄</span>
                <div className="btn-content">
                  <span className="btn-title">ملف جديد</span>
                  <span className="btn-desc">ابدأ الكتابة فوراً</span>
                </div>
              </button>

              <button className="action-btn secondary" onClick={onOpenFile}>
                <span className="btn-icon">📁</span>
                <div className="btn-content">
                  <span className="btn-title">فتح ملف</span>
                  <span className="btn-desc">افتح ملف موجود</span>
                </div>
              </button>

              <button className="action-btn tertiary" onClick={onOpenImageEditor}>
                <span className="btn-icon">🖼️</span>
                <div className="btn-content">
                  <span className="btn-title">محرر الصور</span>
                  <span className="btn-desc">حرر وعدل الصور</span>
                </div>
              </button>
            </div>
          </div>

          <div className="features-preview">
            <h3>الميزات الرئيسية</h3>
            <div className="features-grid">
              <div className="feature-item">
                <span className="feature-icon">✍️</span>
                <span className="feature-text">كتابة باللغة العربية</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🎨</span>
                <span className="feature-text">تحرير الصور</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🔍</span>
                <span className="feature-text">بحث واستبدال</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">💾</span>
                <span className="feature-text">حفظ متعدد الصيغ</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🌈</span>
                <span className="feature-text">فلاتر وتأثيرات</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">⚡</span>
                <span className="feature-text">سرعة وأداء عالي</span>
              </div>
          </div>
        </div>
      </div>

      <div className="welcome-footer">
        <p>تم التطوير بواسطة محمد الاشرافي ❤️ للمجتمع العربي</p>
      </div>
    </div>
  );
};

export default WelcomeScreen;
