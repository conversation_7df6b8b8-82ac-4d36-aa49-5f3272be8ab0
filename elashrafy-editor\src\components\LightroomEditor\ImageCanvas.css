.image-canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #2a2a2a;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  touch-action: none; /* منع التكبير باللمس */
}

.image-canvas {
  display: block;
  max-width: 100%;
  max-height: 100%;
  user-select: none;
  transition: cursor 0.1s ease;
  touch-action: none; /* منع التكبير باللمس */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.image-canvas.dragging {
  cursor: grabbing !important;
}

/* أدوات التحكم في العرض */
.view-controls {
  position: absolute;
  top: 20px;
  right: 20px; /* تغيير من left إلى right */
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 12px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  z-index: 10;
  direction: ltr; /* الأرقام والرموز تبقى LTR */
}

.view-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.view-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.view-btn:active {
  transform: translateY(0);
}

.zoom-level {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
  padding: 0 4px;
}

/* مؤشر المعالجة */
.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 20;
  backdrop-filter: blur(2px);
}

.processing-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-overlay span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

/* رسالة عدم وجود صورة */
.no-image-message {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 5;
}

.no-image-content {
  text-align: center;
  color: #888;
  max-width: 300px;
}

.no-image-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-image-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #aaa;
}

.no-image-content p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* معلومات الصورة */
.image-info {
  position: absolute;
  bottom: 20px;
  right: 20px; /* تغيير من left إلى right */
  display: flex;
  gap: 16px;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 12px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  z-index: 10;
  direction: ltr; /* الأرقام تبقى LTR */
}

.image-info span {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .view-controls {
    top: 10px;
    left: 10px;
    padding: 6px 8px;
    gap: 6px;
  }
  
  .view-btn {
    padding: 4px 6px;
    min-width: 28px;
    height: 28px;
    font-size: 11px;
  }
  
  .zoom-level {
    font-size: 11px;
    min-width: 35px;
  }
  
  .image-info {
    bottom: 10px;
    left: 10px;
    padding: 6px 8px;
    gap: 12px;
  }
  
  .image-info span {
    font-size: 11px;
  }
  
  .no-image-icon {
    font-size: 48px;
  }
  
  .no-image-content h3 {
    font-size: 16px;
  }
  
  .no-image-content p {
    font-size: 13px;
  }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1920px) {
  .view-controls {
    top: 30px;
    left: 30px;
    padding: 10px 16px;
    gap: 10px;
  }
  
  .view-btn {
    padding: 8px 12px;
    min-width: 36px;
    height: 36px;
    font-size: 13px;
  }
  
  .zoom-level {
    font-size: 13px;
    min-width: 45px;
  }
  
  .image-info {
    bottom: 30px;
    left: 30px;
    padding: 10px 16px;
    gap: 20px;
  }
  
  .image-info span {
    font-size: 13px;
  }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
  .view-controls {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid #ffffff;
  }
  
  .view-btn {
    background: #000000;
    border-color: #ffffff;
    color: #ffffff;
  }
  
  .view-btn:hover {
    background: #333333;
  }
  
  .image-info {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid #ffffff;
  }
  
  .processing-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  .view-btn {
    transition: none;
  }
  
  .processing-spinner {
    animation: none;
    border: 3px solid #4a90e2;
  }
  
  .image-canvas {
    transition: none;
  }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
  .image-canvas-container {
    background: #1a1a1a;
  }
  
  .no-image-content {
    color: #666;
  }
  
  .no-image-content h3 {
    color: #888;
  }
}

/* تحسينات إمكانية الوصول */
.view-btn:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

.image-canvas:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}
