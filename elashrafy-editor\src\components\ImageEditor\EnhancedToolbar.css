.enhanced-toolbar {
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  border-bottom: 2px solid #cccccc;
  padding: 15px 20px;
  display: flex;
  gap: 30px;
  align-items: flex-start;
  overflow-x: auto;
  direction: rtl;
  min-height: 100px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: 'Cairo', sans-serif;
}

.toolbar-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: fit-content;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #555;
  text-align: center;
  margin-bottom: 5px;
  font-family: 'Cairo', sans-serif;
}

.toolbar-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  position: relative;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  max-width: 200px;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  max-width: 150px;
}

.tool-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 10px 8px;
  border: 2px solid #ddd;
  border-radius: 8px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 45px;
  font-family: 'Cairo', sans-serif;
}

.tool-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #e8e8e8 0%, #d8d8d8 100%);
  border-color: #666666;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.tool-button.active {
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
  border-color: #666666;
  color: white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
}

.tool-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f0f0f0;
  border-color: #ccc;
}

.tool-icon {
  font-size: 18px;
  line-height: 1;
}

.tool-name {
  font-size: 10px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.filter-button {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border-color: #b3d9ff;
}

.filter-button:hover {
  background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
  border-color: #80bfff;
}

.transform-panel {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 2px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  min-width: 200px;
  margin-top: 5px;
}

.transform-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.control-group label {
  font-size: 12px;
  font-weight: 600;
  color: #555;
  min-width: 50px;
  font-family: 'Cairo', sans-serif;
}

.control-group button {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f8f8f8;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
  font-family: 'Cairo', sans-serif;
}

.control-group button:hover {
  background: #e8e8e8;
  border-color: #999;
}

.apply-btn {
  padding: 10px 15px;
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease;
  font-family: 'Cairo', sans-serif;
}

.apply-btn:hover {
  background: linear-gradient(135deg, #444444 0%, #2a2a2a 100%);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 1024px) {
  .enhanced-toolbar {
    padding: 12px 15px;
    gap: 20px;
    min-height: 80px;
  }
  
  .tools-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 150px;
  }
  
  .filters-grid {
    grid-template-columns: repeat(2, 1fr);
    max-width: 100px;
  }
  
  .tool-button {
    padding: 8px 6px;
    min-width: 40px;
  }
  
  .tool-icon {
    font-size: 16px;
  }
  
  .tool-name {
    font-size: 9px;
  }
}

@media (max-width: 768px) {
  .enhanced-toolbar {
    flex-direction: column;
    align-items: center;
    gap: 15px;
    padding: 15px;
  }
  
  .toolbar-section {
    width: 100%;
    max-width: 300px;
  }
  
  .tools-grid {
    grid-template-columns: repeat(4, 1fr);
    max-width: 100%;
  }
  
  .filters-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 100%;
  }
  
  .transform-panel {
    position: relative;
    top: auto;
    right: auto;
    margin-top: 10px;
    width: 100%;
  }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .enhanced-toolbar {
    background: linear-gradient(135deg, #383838 0%, #2d2d30 100%);
    border-color: #3e3e42;
  }
  
  .section-title {
    color: #cccccc;
  }
  
  .tool-button {
    background: linear-gradient(135deg, #383838 0%, #2d2d30 100%);
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .tool-button:hover:not(.disabled) {
    background: linear-gradient(135deg, #3e3e42 0%, #383838 100%);
    border-color: #6c757d;
  }
  
  .tool-button.disabled {
    background: #2d2d30;
    border-color: #3e3e42;
  }
  
  .filter-button {
    background: linear-gradient(135deg, #2d3748 0%, #2a2f3a 100%);
    border-color: #4a5568;
  }
  
  .filter-button:hover {
    background: linear-gradient(135deg, #3a4a5c 0%, #2d3748 100%);
    border-color: #5a6b7d;
  }
  
  .transform-panel {
    background: #2d2d30;
    border-color: #3e3e42;
  }
  
  .control-group label {
    color: #cccccc;
  }
  
  .control-group button {
    background: #383838;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .control-group button:hover {
    background: #3e3e42;
    border-color: #6c757d;
  }
}
