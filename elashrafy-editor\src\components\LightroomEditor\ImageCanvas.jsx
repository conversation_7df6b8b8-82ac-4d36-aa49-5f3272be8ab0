import { useRef, useEffect, forwardRef, useImperativeHandle, useState, useCallback } from 'react';
import './ImageCanvas.css';

const ImageCanvas = forwardRef(({
  processedImage,
  isProcessing
}, ref) => {
  const canvasRef = useRef(null);
  const containerRef = useRef(null);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  useImperativeHandle(ref, () => ({
    getCanvas: () => canvasRef.current,
    resetView: () => {
      setZoom(1);
      setPan({ x: 0, y: 0 });
    },
    fitToScreen: () => {
      if (processedImage && containerRef.current) {
        const container = containerRef.current;
        const containerWidth = container.clientWidth;
        const containerHeight = container.clientHeight;
        const imageAspect = processedImage.width / processedImage.height;
        const containerAspect = containerWidth / containerHeight;
        
        let newZoom;
        if (imageAspect > containerAspect) {
          newZoom = (containerWidth * 0.9) / processedImage.width;
        } else {
          newZoom = (containerHeight * 0.9) / processedImage.height;
        }
        
        setZoom(Math.max(0.1, Math.min(5, newZoom)));
        setPan({ x: 0, y: 0 });
      }
    }
  }));

  // رسم الصورة على الكانفاس
  useEffect(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !processedImage || !container) return;

    const ctx = canvas.getContext('2d');

    // تعيين أبعاد الكانفاس لتطابق الحاوية
    const containerRect = container.getBoundingClientRect();
    canvas.width = containerRect.width;
    canvas.height = containerRect.height;

    // تحسين جودة الرسم
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // تحسين إضافي للجودة
    const pixelRatio = window.devicePixelRatio || 1;
    canvas.width = containerRect.width * pixelRatio;
    canvas.height = containerRect.height * pixelRatio;
    canvas.style.width = containerRect.width + 'px';
    canvas.style.height = containerRect.height + 'px';
    ctx.scale(pixelRatio, pixelRatio);

    // مسح الكانفاس
    ctx.fillStyle = '#2a2a2a';
    ctx.fillRect(0, 0, containerRect.width, containerRect.height);

    // حساب أبعاد الصورة المعروضة مع التكبير
    const displayWidth = processedImage.naturalWidth || processedImage.width;
    const displayHeight = processedImage.naturalHeight || processedImage.height;

    const scaledWidth = displayWidth * zoom;
    const scaledHeight = displayHeight * zoom;

    // حساب موضع الصورة (في المنتصف + إزاحة السحب)
    const x = (containerRect.width - scaledWidth) / 2 + pan.x;
    const y = (containerRect.height - scaledHeight) / 2 + pan.y;

    // رسم الصورة بالحجم المحسوب
    ctx.drawImage(processedImage, x, y, scaledWidth, scaledHeight);

    // رسم إطار حول الصورة
    ctx.strokeStyle = '#555';
    ctx.lineWidth = 1;
    ctx.strokeRect(x - 1, y - 1, scaledWidth + 2, scaledHeight + 2);

  }, [processedImage, zoom, pan]);

  // معالجة التكبير بالعجلة
  const handleWheel = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();

    // التأكد من أن الحدث يحدث داخل الكانفاس
    if (!canvasRef.current) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    // حساب التكبير الجديد
    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
    const newZoom = Math.max(0.1, Math.min(10, zoom * zoomFactor));

    // حساب الإزاحة الجديدة للحفاظ على نقطة التكبير
    const zoomRatio = newZoom / zoom;
    const newPanX = mouseX - (mouseX - pan.x) * zoomRatio;
    const newPanY = mouseY - (mouseY - pan.y) * zoomRatio;

    setZoom(newZoom);
    setPan({ x: newPanX, y: newPanY });
  }, [zoom, pan]);

  // إضافة مستمع الأحداث مع { passive: false }
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      canvas.removeEventListener('wheel', handleWheel);
    };
  }, [handleWheel]);

  // معالجة بداية السحب
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y });
  };

  // معالجة السحب
  const handleMouseMove = (e) => {
    if (!isDragging) return;
    setPan({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  // معالجة انتهاء السحب
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // معالجة النقر المزدوج للتكبير
  const handleDoubleClick = () => {
    if (zoom === 1) {
      setZoom(2);
    } else {
      setZoom(1);
      setPan({ x: 0, y: 0 });
    }
  };

  // أزرار التحكم في العرض
  const zoomIn = () => {
    const newZoom = Math.min(10, zoom * 1.2);
    setZoom(newZoom);
  };

  const zoomOut = () => {
    const newZoom = Math.max(0.1, zoom / 1.2);
    setZoom(newZoom);
  };

  const resetZoom = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  };

  const fitToScreen = () => {
    if (processedImage && containerRef.current) {
      const container = containerRef.current;
      const containerWidth = container.clientWidth - 40; // هامش
      const containerHeight = container.clientHeight - 40; // هامش

      const imageWidth = processedImage.naturalWidth || processedImage.width;
      const imageHeight = processedImage.naturalHeight || processedImage.height;

      const scaleX = containerWidth / imageWidth;
      const scaleY = containerHeight / imageHeight;
      const newZoom = Math.min(scaleX, scaleY, 1); // لا نكبر أكثر من الحجم الأصلي

      setZoom(newZoom);
      setPan({ x: 0, y: 0 });
    }
  };

  return (
    <div className="image-canvas-container" ref={containerRef}>
      {/* أدوات التحكم في العرض */}
      <div className="view-controls">
        <button className="view-btn" onClick={zoomOut} title="تصغير">
          🔍-
        </button>
        <span className="zoom-level">{Math.round(zoom * 100)}%</span>
        <button className="view-btn" onClick={zoomIn} title="تكبير">
          🔍+
        </button>
        <button className="view-btn" onClick={resetZoom} title="إعادة تعيين العرض">
          ⌂
        </button>
        <button
          className="view-btn"
          onClick={fitToScreen}
          title="ملائمة الشاشة"
        >
          ⛶
        </button>
      </div>

      {/* الكانفاس */}
      <canvas
        ref={canvasRef}
        className={`image-canvas ${isDragging ? 'dragging' : ''}`}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onDoubleClick={handleDoubleClick}
        style={{
          cursor: isDragging ? 'grabbing' : 'grab',
          width: '100%',
          height: '100%',
          display: 'block'
        }}
      />

      {/* مؤشر المعالجة */}
      {isProcessing && (
        <div className="processing-overlay">
          <div className="processing-spinner"></div>
          <span>جاري المعالجة...</span>
        </div>
      )}

      {/* رسالة عدم وجود صورة */}
      {!processedImage && !isProcessing && (
        <div className="no-image-message">
          <div className="no-image-content">
            <div className="no-image-icon">🖼️</div>
            <h3>لا توجد صورة محملة</h3>
            <p>قم بفتح صورة للبدء في التحرير</p>
          </div>
        </div>
      )}

      {/* معلومات الصورة */}
      {processedImage && (
        <div className="image-info">
          <span>{processedImage.width} × {processedImage.height}</span>
          <span>التكبير: {Math.round(zoom * 100)}%</span>
        </div>
      )}
    </div>
  );
});

ImageCanvas.displayName = 'ImageCanvas';

export default ImageCanvas;
