import React, { useState } from 'react';
import './EnhancedToolbar.css';

const EnhancedToolbar = ({ 
  selectedTool, 
  onToolSelect, 
  onUndo, 
  onRedo, 
  onSave,
  onOpenImage,
  transformations,
  onTransformationsChange,
  onApplyTransformations,
  canUndo,
  canRedo
}) => {
  const [showTransformPanel, setShowTransformPanel] = useState(false);

  const tools = [
    { id: 'select', name: 'تحديد', icon: '↖️', description: 'أداة التحديد' },
    { id: 'brush', name: 'فرشاة', icon: '🖌️', description: 'أداة الرسم' },
    { id: 'eraser', name: 'ممحاة', icon: '🧽', description: 'أداة المحو' },
    { id: 'text', name: 'نص', icon: '📝', description: 'إضافة نص' },
    { id: 'rectangle', name: 'مستطيل', icon: '⬜', description: 'رسم مستطيل' },
    { id: 'circle', name: 'دائرة', icon: '⭕', description: 'رسم دائرة' },
    { id: 'line', name: 'خط', icon: '📏', description: 'رسم خط' },
    { id: 'crop', name: 'قص', icon: '✂️', description: 'قص الصورة' }
  ];

  const filters = [
    { id: 'grayscale', name: 'رمادي', icon: '⚫', description: 'تحويل إلى رمادي' },
    { id: 'sepia', name: 'بني قديم', icon: '🟤', description: 'تأثير بني قديم' },
    { id: 'blur', name: 'ضبابي', icon: '🌫️', description: 'تأثير ضبابي' },
    { id: 'brightness', name: 'سطوع', icon: '☀️', description: 'زيادة السطوع' },
    { id: 'contrast', name: 'تباين', icon: '🔆', description: 'زيادة التباين' },
    { id: 'invert', name: 'عكس', icon: '🔄', description: 'عكس الألوان' }
  ];

  const handleFilterApply = (filterId) => {
    const filterData = { type: filterId, value: 1 };
    // سيتم تطبيق الفلتر مباشرة
    console.log('تطبيق فلتر:', filterData);
  };

  return (
    <div className="enhanced-toolbar">
      {/* قسم الملف والحفظ */}
      <div className="toolbar-section">
        <div className="section-title">ملف</div>
        <div className="toolbar-group">
          <button 
            className="tool-button"
            onClick={onOpenImage}
            title="فتح صورة"
          >
            <span className="tool-icon">📁</span>
            <span className="tool-name">فتح</span>
          </button>
          <button 
            className="tool-button"
            onClick={onSave}
            title="حفظ الصورة"
          >
            <span className="tool-icon">💾</span>
            <span className="tool-name">حفظ</span>
          </button>
        </div>
      </div>

      {/* قسم التراجع والإعادة */}
      <div className="toolbar-section">
        <div className="section-title">تحرير</div>
        <div className="toolbar-group">
          <button 
            className={`tool-button ${!canUndo ? 'disabled' : ''}`}
            onClick={onUndo}
            disabled={!canUndo}
            title="تراجع"
          >
            <span className="tool-icon">↶</span>
            <span className="tool-name">تراجع</span>
          </button>
          <button 
            className={`tool-button ${!canRedo ? 'disabled' : ''}`}
            onClick={onRedo}
            disabled={!canRedo}
            title="إعادة"
          >
            <span className="tool-icon">↷</span>
            <span className="tool-name">إعادة</span>
          </button>
        </div>
      </div>

      {/* قسم الأدوات الرئيسية */}
      <div className="toolbar-section">
        <div className="section-title">أدوات الرسم</div>
        <div className="toolbar-group tools-grid">
          {tools.map(tool => (
            <button
              key={tool.id}
              className={`tool-button ${selectedTool === tool.id ? 'active' : ''}`}
              onClick={() => onToolSelect(tool.id)}
              title={tool.description}
            >
              <span className="tool-icon">{tool.icon}</span>
              <span className="tool-name">{tool.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* قسم التحويلات */}
      <div className="toolbar-section">
        <div className="section-title">تحويلات</div>
        <div className="toolbar-group">
          <button 
            className={`tool-button ${showTransformPanel ? 'active' : ''}`}
            onClick={() => setShowTransformPanel(!showTransformPanel)}
            title="إعدادات التحويل"
          >
            <span className="tool-icon">🔧</span>
            <span className="tool-name">إعدادات</span>
          </button>
          
          {showTransformPanel && (
            <div className="transform-panel">
              <div className="transform-controls">
                <div className="control-group">
                  <label>تدوير:</label>
                  <button onClick={() => onTransformationsChange({...transformations, rotation: (transformations.rotation || 0) + 90})}>
                    90°
                  </button>
                  <button onClick={() => onTransformationsChange({...transformations, rotation: (transformations.rotation || 0) - 90})}>
                    -90°
                  </button>
                </div>
                
                <div className="control-group">
                  <label>قلب:</label>
                  <button onClick={() => onTransformationsChange({...transformations, flipX: !transformations.flipX})}>
                    أفقي
                  </button>
                  <button onClick={() => onTransformationsChange({...transformations, flipY: !transformations.flipY})}>
                    عمودي
                  </button>
                </div>
                
                <button 
                  className="apply-btn"
                  onClick={onApplyTransformations}
                >
                  تطبيق التحويلات
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* قسم الفلاتر */}
      <div className="toolbar-section">
        <div className="section-title">فلاتر</div>
        <div className="toolbar-group filters-grid">
          {filters.map(filter => (
            <button
              key={filter.id}
              className="tool-button filter-button"
              onClick={() => handleFilterApply(filter.id)}
              title={filter.description}
            >
              <span className="tool-icon">{filter.icon}</span>
              <span className="tool-name">{filter.name}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EnhancedToolbar;
