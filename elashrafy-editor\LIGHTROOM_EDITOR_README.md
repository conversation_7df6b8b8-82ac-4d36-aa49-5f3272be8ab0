# محرر الصور المتقدم - Lightroom Style Editor

محرر صور احترافي مشابه لـ Adobe Lightroom مع واجهة عربية متطورة وميزات متقدمة لتحرير الصور.

## 🌟 الميزات الرئيسية

### 📸 تحرير الصور المتقدم
- **تعديلات الإضاءة**: التعرض، الإضاءات، الظلال، النقاط البيضاء والسوداء
- **تحسين الألوان**: السطوع، التباين، التشبع، الحيوية
- **توازن الأبيض**: درجة الحرارة والصبغة مع إعدادات مسبقة
- **تحكم HSL**: تعديل التدرج والتشبع والإضاءة لكل لون منفصل
- **تحسين التفاصيل**: زيادة الحدة، إزالة الضوضاء، الوضوح، إزالة الضباب
- **التحويلات**: القص، التدوير، الانعكاس مع نسب محددة مسبقاً

### 🎨 واجهة المستخدم المتطورة
- **تصميم Lightroom**: واجهة مشابهة لـ Adobe Lightroom
- **دعم اللغة العربية**: واجهة كاملة باللغة العربية مع اتجاه RTL
- **معاينة فورية**: تطبيق التعديلات في الوقت الفعلي
- **شرائح تمرير سهلة**: تحكم دقيق في جميع الإعدادات
- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات

### ⚡ الإعدادات المسبقة
- **8 إعدادات احترافية**: طبيعي، دراماتيكي، دافئ، بارد، أبيض وأسود، فينتاج، سينمائي، صورة شخصية
- **تصنيف منظم**: مجموعة حسب النوع (أساسي، فني، كلاسيكي، مزاج)
- **تطبيق سريع**: نقرة واحدة لتطبيق أي إعداد
- **حفظ مخصص**: إمكانية حفظ إعداداتك الخاصة

### 📋 إدارة التاريخ
- **تراجع وإعادة متقدم**: تتبع جميع التعديلات
- **تاريخ مفصل**: عرض جميع الخطوات مع الوقت
- **انتقال سريع**: الانتقال لأي نقطة في التاريخ
- **أيقونات تعبيرية**: تمييز نوع كل تعديل

### 🔧 أدوات متقدمة
- **معاينة قبل/بعد**: مقارنة سريعة للتعديلات
- **تكبير وتصغير**: عرض تفصيلي للصورة
- **تصدير متقدم**: حفظ بصيغ مختلفة مع خيارات الجودة
- **معالجة سريعة**: أداء محسن للصور عالية الدقة

## 🚀 كيفية الاستخدام

### فتح صورة
1. انقر على زر "فتح" في شريط الأدوات
2. اختر صورة من جهازك (PNG, JPG, GIF, WebP, SVG, etc.)
3. ستظهر الصورة في منطقة العرض الرئيسية

### تطبيق التعديلات
1. **اللوحة الأساسية**: تعديل التعرض والألوان الأساسية
2. **لوحة الألوان**: توازن الأبيض وتصحيح الألوان
3. **لوحة HSL**: تحكم دقيق في كل لون منفصل
4. **لوحة التفاصيل**: زيادة الحدة وإزالة الضوضاء
5. **لوحة التحويل**: القص والتدوير والانعكاس

### استخدام الإعدادات المسبقة
1. انقر على لوحة "الإعدادات المسبقة"
2. اختر الفئة المناسبة
3. انقر على الإعداد المطلوب لتطبيقه فوراً

### التصدير
1. انقر على زر "تصدير" في شريط الأدوات
2. اختر صيغة الملف (JPEG, PNG, WebP)
3. حدد الجودة والأبعاد إذا لزم الأمر
4. انقر "تصدير" لحفظ الصورة

## 🎯 الإعدادات المسبقة المتوفرة

### أساسي
- **طبيعي**: تحسين طبيعي للصور العادية
- **صورة شخصية**: محسن للصور الشخصية

### فني
- **دراماتيكي**: تباين عالي وألوان قوية
- **سينمائي**: مظهر سينمائي احترافي

### كلاسيكي
- **أبيض وأسود**: تحويل احترافي للأبيض والأسود
- **فينتاج**: مظهر كلاسيكي قديم

### مزاج
- **دافئ**: ألوان دافئة ومريحة
- **بارد**: ألوان باردة ومنعشة

## ⌨️ اختصارات لوحة المفاتيح

- `Ctrl + I`: فتح محرر الصور
- `Ctrl + S`: حفظ/تصدير
- `Ctrl + Z`: تراجع
- `Ctrl + Y`: إعادة
- `مسافة`: تبديل معاينة قبل/بعد

## 🔧 التقنيات المستخدمة

- **React 18**: مكتبة واجهة المستخدم
- **Canvas API**: معالجة الصور
- **CSS Grid/Flexbox**: تخطيط متجاوب
- **Web Workers**: معالجة متوازية (قريباً)

## 📱 التوافق

- **المتصفحات**: Chrome, Firefox, Safari, Edge
- **الأجهزة**: سطح المكتب، اللوحي، الهاتف
- **أنظمة التشغيل**: Windows, macOS, Linux, iOS, Android

## 🎨 التخصيص

### الألوان
يمكن تخصيص ألوان الواجهة من خلال متغيرات CSS:
```css
:root {
  --primary-color: #4a90e2;
  --background-color: #1a1a1a;
  --panel-color: #2a2a2a;
}
```

### الإعدادات المسبقة
يمكن إضافة إعدادات مسبقة جديدة في ملف `PresetPanel.jsx`:
```javascript
{
  name: 'إعداد مخصص',
  category: 'مخصص',
  thumbnail: '🎨',
  adjustments: {
    // قيم التعديلات هنا
  }
}
```

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشكلة أو لديك اقتراح للتحسين:
1. تأكد من استخدام متصفح محدث
2. تحقق من وحدة تحكم المطور للأخطاء
3. أرسل تقرير مفصل عن المشكلة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## 🙏 الشكر والتقدير

- Adobe Lightroom للإلهام في التصميم
- مجتمع React للأدوات والمكتبات
- مطوري CSS Grid و Flexbox

---

**ملاحظة**: هذا محرر تعليمي وليس بديلاً كاملاً لبرامج التحرير الاحترافية. للاستخدام التجاري المتقدم، يُنصح باستخدام برامج متخصصة.
