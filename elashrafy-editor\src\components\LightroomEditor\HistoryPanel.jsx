import { useState } from 'react';
import './HistoryPanel.css';

const HistoryPanel = ({ history, historyIndex, onGoToHistory }) => {
  const [isCollapsed, setIsCollapsed] = useState(true);

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getActionIcon = (action) => {
    if (action.includes('فتح')) return '📁';
    if (action.includes('تعرض') || action.includes('سطوع')) return '☀️';
    if (action.includes('لون') || action.includes('تشبع')) return '🎨';
    if (action.includes('قص') || action.includes('تدوير')) return '✂️';
    if (action.includes('حدة') || action.includes('ضوضاء')) return '🔍';
    if (action.includes('إعداد مسبق')) return '⚡';
    if (action.includes('إعادة تعيين')) return '🔄';
    return '✏️';
  };

  return (
    <div className={`history-panel ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="history-header" onClick={() => setIsCollapsed(!isCollapsed)}>
        <h3 className="history-title">
          التاريخ
          {history.length > 0 && (
            <span className="history-count">({history.length})</span>
          )}
        </h3>
        <button className={`collapse-btn ${isCollapsed ? 'collapsed' : ''}`}>
          ▼
        </button>
      </div>

      {!isCollapsed && (
        <div className="history-content">
          {history.length === 0 ? (
            <div className="empty-history">
              <div className="empty-icon">📝</div>
              <p>لا توجد تعديلات بعد</p>
              <small>ستظهر هنا جميع التعديلات التي تقوم بها</small>
            </div>
          ) : (
            <div className="history-list">
              {history.map((item, index) => (
                <div
                  key={item.id}
                  className={`history-item ${index === historyIndex ? 'active' : ''} ${index > historyIndex ? 'future' : ''}`}
                  onClick={() => onGoToHistory(index)}
                  title={`الانتقال إلى: ${item.action}`}
                >
                  <div className="history-icon">
                    {getActionIcon(item.action)}
                  </div>
                  
                  <div className="history-details">
                    <div className="history-action">{item.action}</div>
                    <div className="history-time">{formatTime(item.timestamp)}</div>
                  </div>
                  
                  <div className="history-indicator">
                    {index === historyIndex && <div className="current-indicator">●</div>}
                  </div>
                </div>
              ))}
            </div>
          )}

          {history.length > 0 && (
            <div className="history-actions">
              <div className="history-info">
                <span className="current-step">
                  الخطوة {historyIndex + 1} من {history.length}
                </span>
              </div>
              
              <div className="history-controls">
                <button
                  className="history-btn"
                  onClick={() => onGoToHistory(0)}
                  disabled={historyIndex === 0}
                  title="العودة للبداية"
                >
                  ⏮️
                </button>
                
                <button
                  className="history-btn"
                  onClick={() => onGoToHistory(Math.max(0, historyIndex - 1))}
                  disabled={historyIndex === 0}
                  title="الخطوة السابقة"
                >
                  ⏪
                </button>
                
                <button
                  className="history-btn"
                  onClick={() => onGoToHistory(Math.min(history.length - 1, historyIndex + 1))}
                  disabled={historyIndex === history.length - 1}
                  title="الخطوة التالية"
                >
                  ⏩
                </button>
                
                <button
                  className="history-btn"
                  onClick={() => onGoToHistory(history.length - 1)}
                  disabled={historyIndex === history.length - 1}
                  title="الانتقال للنهاية"
                >
                  ⏭️
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default HistoryPanel;
