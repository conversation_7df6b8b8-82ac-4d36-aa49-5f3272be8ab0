import './StatusBar.css';

const StatusBar = ({ 
  content, 
  fileName, 
  isModified,
  selectionStart,
  selectionEnd 
}) => {
  const lines = content.split('\n');
  const totalLines = lines.length;
  const totalChars = content.length;
  const totalWords = content.trim() ? content.trim().split(/\s+/).length : 0;
  
  // حساب موقع المؤشر
  const getCurrentLineAndColumn = () => {
    if (selectionStart === undefined) return { line: 1, column: 1 };
    
    const textBeforeCursor = content.substring(0, selectionStart);
    const linesBeforeCursor = textBeforeCursor.split('\n');
    const currentLine = linesBeforeCursor.length;
    const currentColumn = linesBeforeCursor[linesBeforeCursor.length - 1].length + 1;
    
    return { line: currentLine, column: currentColumn };
  };

  const { line, column } = getCurrentLineAndColumn();
  const hasSelection = selectionStart !== selectionEnd;
  const selectedLength = hasSelection ? selectionEnd - selectionStart : 0;

  return (
    <div className="status-bar">
      <div className="status-left">
        <span className="file-info">
          {fileName}{isModified ? ' •' : ''}
        </span>
      </div>
      
      <div className="status-center">
        {hasSelection && (
          <span className="selection-info">
            محدد: {selectedLength} حرف
          </span>
        )}
      </div>
      
      <div className="status-right">
        <span className="cursor-info">
          السطر {line}، العمود {column}
        </span>
        <span className="separator">|</span>
        <span className="document-info">
          {totalLines} سطر، {totalWords} كلمة، {totalChars} حرف
        </span>
        <span className="separator">|</span>
        <span className="encoding-info">
          UTF-8
        </span>
      </div>
    </div>
  );
};

export default StatusBar;
