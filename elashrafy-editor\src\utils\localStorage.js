// نظام إدارة التخزين المحلي للتطبيق

const STORAGE_KEYS = {
  CONTENT: 'elashrafy_editor_content',
  FILENAME: 'elashrafy_editor_filename',
  HISTORY: 'elashrafy_editor_history',
  HISTORY_INDEX: 'elashrafy_editor_history_index',
  SETTINGS: 'elashrafy_editor_settings',
  LAST_SAVE: 'elashrafy_editor_last_save'
};

// حفظ البيانات في localStorage
export const saveToLocalStorage = (key, data) => {
  try {
    const serializedData = JSON.stringify(data);
    localStorage.setItem(key, serializedData);
    localStorage.setItem(STORAGE_KEYS.LAST_SAVE, new Date().toISOString());
    return true;
  } catch (error) {
    console.error('خطأ في حفظ البيانات:', error);
    return false;
  }
};

// استرجاع البيانات من localStorage
export const loadFromLocalStorage = (key, defaultValue = null) => {
  try {
    const serializedData = localStorage.getItem(key);
    if (serializedData === null || serializedData === undefined || serializedData === '') {
      return defaultValue;
    }

    // التحقق من صحة JSON قبل التحليل
    if (typeof serializedData === 'string' && serializedData.trim().length > 0) {
      return JSON.parse(serializedData);
    }
    return defaultValue;
  } catch (error) {
    console.error('خطأ في استرجاع البيانات:', error);
    // مسح البيانات التالفة
    localStorage.removeItem(key);
    return defaultValue;
  }
};

// حذف البيانات من localStorage
export const removeFromLocalStorage = (key) => {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('خطأ في حذف البيانات:', error);
    return false;
  }
};

// حفظ حالة المحرر
export const saveEditorState = (content, fileName, history, historyIndex) => {
  const state = {
    content,
    fileName,
    history,
    historyIndex,
    timestamp: new Date().toISOString()
  };
  
  saveToLocalStorage(STORAGE_KEYS.CONTENT, content);
  saveToLocalStorage(STORAGE_KEYS.FILENAME, fileName);
  saveToLocalStorage(STORAGE_KEYS.HISTORY, history);
  saveToLocalStorage(STORAGE_KEYS.HISTORY_INDEX, historyIndex);
  
  return state;
};

// استرجاع حالة المحرر
export const loadEditorState = () => {
  try {
    const content = loadFromLocalStorage(STORAGE_KEYS.CONTENT, '');
    const fileName = loadFromLocalStorage(STORAGE_KEYS.FILENAME, 'مستند جديد');
    const history = loadFromLocalStorage(STORAGE_KEYS.HISTORY, ['']);
    const historyIndex = loadFromLocalStorage(STORAGE_KEYS.HISTORY_INDEX, 0);
    const lastSave = loadFromLocalStorage(STORAGE_KEYS.LAST_SAVE, null);

    return {
      content: content || '',
      fileName: fileName || 'مستند جديد',
      history: Array.isArray(history) ? history : [''],
      historyIndex: typeof historyIndex === 'number' ? historyIndex : 0,
      lastSave
    };
  } catch (error) {
    console.error('خطأ في تحميل حالة المحرر:', error);
    return {
      content: '',
      fileName: 'مستند جديد',
      history: [''],
      historyIndex: 0,
      lastSave: null
    };
  }
};

// حفظ إعدادات التطبيق
export const saveSettings = (settings) => {
  return saveToLocalStorage(STORAGE_KEYS.SETTINGS, settings);
};

// استرجاع إعدادات التطبيق
export const loadSettings = () => {
  return loadFromLocalStorage(STORAGE_KEYS.SETTINGS, {
    fontSize: 14,
    fontFamily: 'Cairo',
    theme: 'light',
    autoSave: true,
    autoSaveInterval: 30000 // 30 ثانية
  });
};

// مسح جميع البيانات المحفوظة
export const clearAllData = () => {
  Object.values(STORAGE_KEYS).forEach(key => {
    removeFromLocalStorage(key);
  });
};

// فحص حجم البيانات المحفوظة
export const getStorageSize = () => {
  let totalSize = 0;
  Object.values(STORAGE_KEYS).forEach(key => {
    const data = localStorage.getItem(key);
    if (data) {
      totalSize += data.length;
    }
  });
  return totalSize;
};

// فحص إذا كان التخزين المحلي متاح
export const isLocalStorageAvailable = () => {
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch (error) {
    return false;
  }
};

// تصدير مفاتيح التخزين للاستخدام في مكونات أخرى
export { STORAGE_KEYS };

// نظام النسخ الاحتياطي التلقائي
export const createBackup = () => {
  const editorState = loadEditorState();
  const settings = loadSettings();
  
  const backup = {
    version: '1.0',
    timestamp: new Date().toISOString(),
    editorState,
    settings
  };
  
  return backup;
};

// استعادة من النسخة الاحتياطية
export const restoreFromBackup = (backup) => {
  try {
    if (backup.editorState) {
      const { content, fileName, history, historyIndex } = backup.editorState;
      saveEditorState(content, fileName, history, historyIndex);
    }
    
    if (backup.settings) {
      saveSettings(backup.settings);
    }
    
    return true;
  } catch (error) {
    console.error('خطأ في استعادة النسخة الاحتياطية:', error);
    return false;
  }
};

// Hook للحفظ التلقائي
export const useAutoSave = (data, interval = 30000) => {
  const [lastSaved, setLastSaved] = React.useState(null);
  
  React.useEffect(() => {
    if (!data) return;
    
    const autoSaveTimer = setInterval(() => {
      if (data.content !== undefined) {
        saveEditorState(data.content, data.fileName, data.history, data.historyIndex);
        setLastSaved(new Date());
        console.log('تم الحفظ التلقائي:', new Date().toLocaleTimeString());
      }
    }, interval);
    
    return () => clearInterval(autoSaveTimer);
  }, [data, interval]);
  
  return lastSaved;
};
