.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #1a1a1a;
  border-bottom: 1px solid #333;
  height: 60px;
  box-sizing: border-box;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-left {
  flex: 1;
  justify-content: flex-start;
}

.toolbar-center {
  flex: 2;
  justify-content: center;
}

.toolbar-right {
  flex: 1;
  justify-content: flex-start; /* تغيير من flex-end إلى flex-start للـ RTL */
}

/* شعار التطبيق */
.app-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.app-icon {
  font-size: 20px;
}

/* معلومات الملف */
.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  font-size: 14px;
  color: #cccccc;
  background: #333;
  padding: 4px 8px;
  border-radius: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* أدوات التحكم الرئيسية */
.main-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #2a2a2a;
  padding: 6px;
  border-radius: 8px;
  border: 1px solid #333;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: #cccccc;
  cursor: pointer;
  font-size: 13px;
  font-family: inherit;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.toolbar-btn:hover {
  background: #3a3a3a;
  color: #ffffff;
}

.toolbar-btn:active {
  background: #4a4a4a;
}

.toolbar-btn:disabled {
  color: #666;
  cursor: not-allowed;
  background: transparent;
}

.toolbar-btn.active {
  background: #4a90e2;
  color: #ffffff;
}

.toolbar-btn.active:hover {
  background: #5ba0f2;
}

.btn-icon {
  font-size: 16px;
}

.btn-text {
  font-size: 12px;
  font-weight: 500;
}

.btn-primary {
  background: #4a90e2;
  color: #ffffff;
}

.btn-primary:hover {
  background: #5ba0f2;
}

.btn-secondary {
  background: #555;
  color: #cccccc;
}

.btn-secondary:hover {
  background: #666;
  color: #ffffff;
}

/* فاصل */
.separator {
  width: 1px;
  height: 24px;
  background: #444;
  margin: 0 4px;
}

/* أدوات التصدير */
.export-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* نافذة التصدير */
.export-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.export-dialog {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #444;
  background: #333;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: #ffffff;
}

.close-btn {
  background: none;
  border: none;
  color: #ccc;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #444;
  color: #fff;
}

.dialog-content {
  padding: 24px;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-label {
  font-size: 14px;
  color: #cccccc;
  font-weight: 500;
}

.option-select {
  padding: 8px 12px;
  background: #333;
  border: 1px solid #555;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
}

.option-select:focus {
  outline: none;
  border-color: #4a90e2;
}

.quality-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #444;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.quality-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.quality-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dimensions-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dimension-input {
  flex: 1;
  padding: 8px 12px;
  background: #333;
  border: 1px solid #555;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
}

.dimension-input:focus {
  outline: none;
  border-color: #4a90e2;
}

.dimension-separator {
  color: #ccc;
  font-size: 16px;
  font-weight: bold;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #cccccc;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #4a90e2;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #444;
  background: #333;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .toolbar {
    padding: 8px 12px;
    height: auto;
    flex-direction: column;
    gap: 8px;
  }
  
  .toolbar-section {
    width: 100%;
    justify-content: center;
  }
  
  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    flex: none;
  }
  
  .main-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .btn-text {
    display: none;
  }
  
  .toolbar-btn {
    padding: 8px;
    min-width: 40px;
    justify-content: center;
  }
  
  .file-name {
    max-width: 150px;
  }
  
  .export-dialog {
    width: 95%;
    margin: 10px;
  }
  
  .dialog-content {
    padding: 16px;
  }
  
  .dialog-header,
  .dialog-footer {
    padding: 16px;
  }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
  .toolbar {
    border-bottom-color: #ffffff;
  }
  
  .main-controls {
    border-color: #ffffff;
  }
  
  .toolbar-btn {
    border: 1px solid transparent;
  }
  
  .toolbar-btn:hover {
    border-color: #ffffff;
  }
  
  .export-dialog {
    border-color: #ffffff;
  }
}

/* زر العودة للصفحة الرئيسية */
.back-btn {
  background: linear-gradient(45deg, #4a90e2, #5ba0f2) !important;
  border: 2px solid rgba(74, 144, 226, 0.3) !important;
  color: white !important;
}

.back-btn:hover {
  background: linear-gradient(45deg, #5ba0f2, #6bb0ff) !important;
  border-color: rgba(74, 144, 226, 0.5) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.4) !important;
}

.back-btn .btn-icon {
  color: white;
}

.back-btn .btn-text {
  color: white;
  font-weight: 600;
}

.separator {
  width: 1px;
  height: 20px;
  background: #444;
  margin: 0 8px;
}
