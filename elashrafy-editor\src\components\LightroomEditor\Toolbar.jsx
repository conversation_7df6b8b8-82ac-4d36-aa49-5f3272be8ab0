import { useState } from 'react';
import './Toolbar.css';

const Toolbar = ({
  fileName,
  onOpenFile,
  onSave,
  onClose,
  onBackToWelcome,
  onUndo,
  onRedo,
  onReset,
  canUndo,
  canRedo,
  showBeforeAfter,
  onToggleBeforeAfter
}) => {
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [exportSettings, setExportSettings] = useState({
    format: 'jpeg',
    quality: 90,
    width: '',
    height: '',
    maintainAspect: true
  });

  const handleExport = () => {
    onSave(exportSettings.format, exportSettings.quality);
    setShowExportDialog(false);
  };

  return (
    <div className="toolbar">
      <div className="toolbar-section toolbar-left">
        {/* زر العودة للصفحة الرئيسية */}
        <button
          className="toolbar-btn back-btn"
          onClick={onBackToWelcome}
          title="العودة للصفحة الرئيسية"
        >
          <span className="btn-icon">🏠</span>
          <span className="btn-text">الرئيسية</span>
        </button>

        <div className="separator"></div>

        {/* شعار وعنوان */}
        <div className="app-title">
          <span className="app-icon">📸</span>
          <span>محرر الصور المتقدم</span>
        </div>

        {/* اسم الملف */}
        {fileName && (
          <div className="file-info">
            <span className="file-name">{fileName}</span>
          </div>
        )}
      </div>

      <div className="toolbar-section toolbar-center">
        {/* أدوات التحكم الرئيسية */}
        <div className="main-controls">
          <button 
            className="toolbar-btn"
            onClick={onOpenFile}
            title="فتح صورة"
          >
            <span className="btn-icon">📁</span>
            <span className="btn-text">فتح</span>
          </button>

          <div className="separator"></div>

          <button 
            className="toolbar-btn"
            onClick={onUndo}
            disabled={!canUndo}
            title="تراجع"
          >
            <span className="btn-icon">↶</span>
            <span className="btn-text">تراجع</span>
          </button>

          <button 
            className="toolbar-btn"
            onClick={onRedo}
            disabled={!canRedo}
            title="إعادة"
          >
            <span className="btn-icon">↷</span>
            <span className="btn-text">إعادة</span>
          </button>

          <div className="separator"></div>

          <button 
            className="toolbar-btn"
            onClick={onReset}
            title="إعادة تعيين جميع التعديلات"
          >
            <span className="btn-icon">🔄</span>
            <span className="btn-text">إعادة تعيين</span>
          </button>

          <div className="separator"></div>

          <button 
            className={`toolbar-btn ${showBeforeAfter ? 'active' : ''}`}
            onClick={onToggleBeforeAfter}
            title="مقارنة قبل وبعد"
          >
            <span className="btn-icon">👁️</span>
            <span className="btn-text">قبل/بعد</span>
          </button>
        </div>
      </div>

      <div className="toolbar-section toolbar-right">
        {/* أدوات الحفظ والتصدير */}
        <div className="export-controls">
          <button 
            className="toolbar-btn btn-primary"
            onClick={() => setShowExportDialog(true)}
            disabled={!fileName}
            title="تصدير الصورة"
          >
            <span className="btn-icon">💾</span>
            <span className="btn-text">تصدير</span>
          </button>

          <button 
            className="toolbar-btn btn-secondary"
            onClick={onClose}
            title="إغلاق المحرر"
          >
            <span className="btn-icon">✕</span>
            <span className="btn-text">إغلاق</span>
          </button>
        </div>
      </div>

      {/* نافذة التصدير */}
      {showExportDialog && (
        <div className="export-dialog-overlay">
          <div className="export-dialog">
            <div className="dialog-header">
              <h3>تصدير الصورة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowExportDialog(false)}
              >
                ✕
              </button>
            </div>

            <div className="dialog-content">
              <div className="export-options">
                <div className="option-group">
                  <label className="option-label">صيغة الملف:</label>
                  <select 
                    value={exportSettings.format}
                    onChange={(e) => setExportSettings(prev => ({
                      ...prev, 
                      format: e.target.value
                    }))}
                    className="option-select"
                  >
                    <option value="jpeg">JPEG</option>
                    <option value="png">PNG</option>
                    <option value="webp">WebP</option>
                  </select>
                </div>

                {exportSettings.format === 'jpeg' && (
                  <div className="option-group">
                    <label className="option-label">
                      الجودة: {exportSettings.quality}%
                    </label>
                    <input
                      type="range"
                      min="10"
                      max="100"
                      value={exportSettings.quality}
                      onChange={(e) => setExportSettings(prev => ({
                        ...prev, 
                        quality: parseInt(e.target.value)
                      }))}
                      className="quality-slider"
                    />
                  </div>
                )}

                <div className="option-group">
                  <label className="option-label">الأبعاد:</label>
                  <div className="dimensions-inputs">
                    <input
                      type="number"
                      placeholder="العرض"
                      value={exportSettings.width}
                      onChange={(e) => setExportSettings(prev => ({
                        ...prev, 
                        width: e.target.value
                      }))}
                      className="dimension-input"
                    />
                    <span className="dimension-separator">×</span>
                    <input
                      type="number"
                      placeholder="الارتفاع"
                      value={exportSettings.height}
                      onChange={(e) => setExportSettings(prev => ({
                        ...prev, 
                        height: e.target.value
                      }))}
                      className="dimension-input"
                    />
                  </div>
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={exportSettings.maintainAspect}
                      onChange={(e) => setExportSettings(prev => ({
                        ...prev, 
                        maintainAspect: e.target.checked
                      }))}
                    />
                    الحفاظ على النسبة
                  </label>
                </div>
              </div>
            </div>

            <div className="dialog-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowExportDialog(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={handleExport}
              >
                تصدير
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Toolbar;
