import React, { useState } from 'react';
import './ImageFilters.css';

const ImageFilters = ({ onFilterApply }) => {
  const [selectedFilter, setSelectedFilter] = useState(null);

  const filters = [
    {
      id: 'none',
      name: 'بدون فلتر',
      preview: '🖼️',
      description: 'الصورة الأصلية بدون تعديل'
    },
    {
      id: 'grayscale',
      name: 'أبيض وأسود',
      preview: '⚫',
      description: 'تحويل الصورة إلى أبيض وأسود'
    },
    {
      id: 'sepia',
      name: 'سيبيا',
      preview: '🟤',
      description: 'تأثير الصور القديمة'
    },
    {
      id: 'vintage',
      name: 'كلاسيكي',
      preview: '📸',
      description: 'تأثير الصور الكلاسيكية'
    },
    {
      id: 'cool',
      name: 'بارد',
      preview: '🧊',
      description: 'تأثير الألوان الباردة'
    },
    {
      id: 'warm',
      name: 'دافئ',
      preview: '🔥',
      description: 'تأثير الألوان الدافئة'
    },
    {
      id: 'dramatic',
      name: 'درامي',
      preview: '🎭',
      description: 'تأثير درامي عالي التباين'
    },
    {
      id: 'soft',
      name: 'ناعم',
      preview: '☁️',
      description: 'تأثير ناعم ومريح'
    },
    {
      id: 'vibrant',
      name: 'نابض',
      preview: '🌈',
      description: 'ألوان نابضة بالحياة'
    },
    {
      id: 'noir',
      name: 'نوار',
      preview: '🕴️',
      description: 'تأثير الأفلام السوداء'
    },
    {
      id: 'polaroid',
      name: 'بولارويد',
      preview: '📷',
      description: 'تأثير كاميرا بولارويد'
    },
    {
      id: 'cross_process',
      name: 'معالجة متقاطعة',
      preview: '🎨',
      description: 'تأثير المعالجة المتقاطعة'
    }
  ];

  const handleFilterSelect = (filter) => {
    setSelectedFilter(filter.id);
    
    // تطبيق الفلتر
    const filterData = getFilterData(filter.id);
    onFilterApply(filterData);
  };

  const getFilterData = (filterId) => {
    const filterConfigs = {
      none: {
        type: 'none'
      },
      grayscale: {
        type: 'grayscale'
      },
      sepia: {
        type: 'sepia'
      },
      vintage: {
        type: 'sepia',
        intensity: 0.8
      },
      cool: {
        type: 'temperature',
        temperature: -20
      },
      warm: {
        type: 'temperature',
        temperature: 20
      },
      dramatic: {
        type: 'contrast',
        amount: 40
      },
      soft: {
        type: 'blur',
        amount: 2
      },
      vibrant: {
        type: 'saturation',
        amount: 30
      },
      noir: {
        type: 'grayscale'
      },
      polaroid: {
        type: 'vintage'
      },
      cross_process: {
        type: 'invert'
      }
    };

    return filterConfigs[filterId] || filterConfigs.none;
  };

  return (
    <div className="image-filters">
      <div className="filters-header">
        <h3>الفلاتر والتأثيرات</h3>
        <button 
          className="clear-filter-button"
          onClick={() => handleFilterSelect(filters[0])}
          disabled={selectedFilter === 'none' || selectedFilter === null}
        >
          🗑️ إزالة الفلتر
        </button>
      </div>

      <div className="filters-content">
        <div className="filters-grid">
          {filters.map(filter => (
            <div
              key={filter.id}
              className={`filter-item ${selectedFilter === filter.id ? 'active' : ''}`}
              onClick={() => handleFilterSelect(filter)}
              title={filter.description}
            >
              <div className="filter-preview">
                <span className="filter-icon">{filter.preview}</span>
              </div>
              <div className="filter-name">{filter.name}</div>
            </div>
          ))}
        </div>

        {/* إعدادات الفلتر المتقدمة */}
        {selectedFilter && selectedFilter !== 'none' && (
          <div className="filter-settings">
            <h4>إعدادات الفلتر</h4>
            
            <div className="setting-group">
              <label>شدة التأثير:</label>
              <input
                type="range"
                min="0"
                max="100"
                defaultValue="100"
                className="range-input"
                onChange={(e) => {
                  const intensity = parseInt(e.target.value) / 100;
                  const filterData = getFilterData(selectedFilter);
                  filterData.intensity = intensity;
                  onFilterApply(filterData);
                }}
              />
            </div>

            {/* إعدادات إضافية حسب نوع الفلتر */}
            {(selectedFilter === 'vintage' || selectedFilter === 'dramatic' || selectedFilter === 'soft') && (
              <>
                <div className="setting-group">
                  <label>السطوع:</label>
                  <input
                    type="range"
                    min="-50"
                    max="50"
                    defaultValue="0"
                    className="range-input"
                  />
                </div>
                
                <div className="setting-group">
                  <label>التباين:</label>
                  <input
                    type="range"
                    min="-50"
                    max="50"
                    defaultValue="0"
                    className="range-input"
                  />
                </div>
              </>
            )}

            {(selectedFilter === 'cool' || selectedFilter === 'warm') && (
              <div className="setting-group">
                <label>درجة الحرارة:</label>
                <input
                  type="range"
                  min="-50"
                  max="50"
                  defaultValue="0"
                  className="range-input"
                />
              </div>
            )}
          </div>
        )}

        {/* فلاتر مخصصة */}
        <div className="custom-filters">
          <h4>فلاتر مخصصة</h4>
          
          <div className="custom-filter-controls">
            <button
              className="custom-filter-button"
              onClick={() => onFilterApply({ type: 'blur', amount: 3 })}
            >
              🌫️ ضبابي
            </button>

            <button
              className="custom-filter-button"
              onClick={() => onFilterApply({ type: 'invert' })}
            >
              � عكس الألوان
            </button>

            <button
              className="custom-filter-button"
              onClick={() => onFilterApply({ type: 'grayscale' })}
            >
              ⚫ أبيض وأسود
            </button>

            <button
              className="custom-filter-button"
              onClick={() => onFilterApply({ type: 'sepia' })}
            >
              � سيبيا
            </button>
          </div>
        </div>

      </div>
    </div>
  );
};

export default ImageFilters;
