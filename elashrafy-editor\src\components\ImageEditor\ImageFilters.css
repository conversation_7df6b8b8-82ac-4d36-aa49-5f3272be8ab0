.image-filters {
  padding: 16px;
  background: white;
  direction: rtl;
  border-top: 1px solid #e9ecef;
  flex-shrink: 0;
  max-height: 40vh;
  overflow-y: auto;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;
}

.filters-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.clear-filter-button {
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #6c757d;
  transition: all 0.2s ease;
}

.clear-filter-button:hover:not(:disabled) {
  background: #e9ecef;
  color: #495057;
}

.clear-filter-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.filters-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 8px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 8px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.filter-item:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2);
}

.filter-item.active {
  background: #2196f3;
  border-color: #1976d2;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

.filter-preview {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.filter-item.active .filter-preview {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.filter-icon {
  font-size: 20px;
}

.filter-name {
  font-size: 11px;
  font-weight: 500;
  line-height: 1.2;
}

.filter-settings {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.filter-settings h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 8px;
}

.setting-group {
  margin-bottom: 12px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 6px;
}

.range-input {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: #dee2e6;
  outline: none;
  -webkit-appearance: none;
}

.range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #2196f3;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-input::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #2196f3;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-filters {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.custom-filters h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 8px;
}

.custom-filter-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
}

.custom-filter-button {
  padding: 10px 12px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.custom-filter-button:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  transform: translateY(-1px);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .image-filters {
    padding: 12px;
  }
  
  .filters-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .clear-filter-button {
    width: 100%;
  }
  
  .filters-grid {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
    gap: 6px;
  }
  
  .filter-item {
    padding: 8px 6px;
    gap: 4px;
  }
  
  .filter-preview {
    width: 32px;
    height: 32px;
  }
  
  .filter-icon {
    font-size: 16px;
  }
  
  .filter-name {
    font-size: 10px;
  }
  
  .filter-settings,
  .custom-filters {
    padding: 12px;
  }
  
  .custom-filter-controls {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 6px;
  }
  
  .custom-filter-button {
    padding: 8px 6px;
    font-size: 11px;
  }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .image-filters {
    background: #252526;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .filters-header {
    border-color: #3e3e42;
  }
  
  .filters-header h3 {
    color: #cccccc;
  }
  
  .clear-filter-button {
    background: #383838;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .clear-filter-button:hover:not(:disabled) {
    background: #3e3e42;
  }
  
  .filter-item {
    background: #2d2d30;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .filter-item:hover {
    background: #1e3a8a;
    border-color: #2196f3;
  }
  
  .filter-preview {
    background: #383838;
    border-color: #3e3e42;
  }
  
  .filter-item.active .filter-preview {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .filter-settings,
  .custom-filters {
    background: #2d2d30;
    border-color: #3e3e42;
  }
  
  .filter-settings h4,
  .custom-filters h4 {
    color: #cccccc;
    border-color: #3e3e42;
  }
  
  .setting-group label {
    color: #cccccc;
  }
  
  .range-input {
    background: #3e3e42;
  }
  
  .custom-filter-button {
    background: #383838;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .custom-filter-button:hover {
    background: #1e3a8a;
    border-color: #2196f3;
  }
}

/* تحسينات إمكانية الوصول */
.filter-item:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}

.range-input:focus {
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.3);
}

.custom-filter-button:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}

/* تأثيرات التحميل */
.filter-item.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}
