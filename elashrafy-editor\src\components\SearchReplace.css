.search-replace-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 100px;
  z-index: 1000;
  direction: rtl;
}

.search-replace-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  min-width: 400px;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: #e9ecef;
  color: #333;
}

.search-controls {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-group label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.input-with-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.search-input,
.replace-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  direction: auto;
}

.search-input:focus,
.replace-input:focus {
  outline: none;
  border-color: #007acc;
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

.search-buttons,
.replace-buttons {
  display: flex;
  gap: 4px;
}

.nav-btn {
  padding: 6px 10px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.replace-btn,
.replace-all-btn {
  padding: 6px 12px;
  background-color: #007acc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.replace-btn:hover,
.replace-all-btn:hover {
  background-color: #005a9e;
}

.replace-all-btn {
  background-color: #28a745;
}

.replace-all-btn:hover {
  background-color: #218838;
}

.options-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.checkbox-label span {
  user-select: none;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .search-replace-overlay {
    padding: 20px;
    align-items: center;
  }
  
  .search-replace-panel {
    min-width: auto;
    width: 100%;
    max-width: none;
  }
  
  .input-with-buttons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-buttons,
  .replace-buttons {
    justify-content: center;
  }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .search-replace-panel {
    background: #2d2d30;
    color: #cccccc;
  }
  
  .panel-header {
    background-color: #383838;
    border-color: #3e3e42;
  }
  
  .panel-header h3 {
    color: #cccccc;
  }
  
  .close-btn {
    color: #cccccc;
  }
  
  .close-btn:hover {
    background-color: #3e3e42;
  }
  
  .search-input,
  .replace-input {
    background-color: #1e1e1e;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .search-input:focus,
  .replace-input:focus {
    border-color: #007acc;
  }
  
  .nav-btn {
    background-color: #383838;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .nav-btn:hover {
    background-color: #3e3e42;
  }
  
  .options-group {
    border-color: #3e3e42;
  }
  
  .checkbox-label {
    color: #cccccc;
  }
}
