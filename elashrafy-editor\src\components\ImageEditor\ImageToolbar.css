.image-toolbar {
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  border-bottom: 2px solid #cccccc;
  padding: 12px 16px;
  display: flex;
  gap: 24px;
  align-items: flex-start;
  overflow-x: auto;
  direction: rtl;
  min-height: 80px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.toolbar-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: fit-content;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 4px;
}

.toolbar-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 6px;
  max-width: 300px;
}

.tool-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 6px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 50px;
  font-size: 11px;
}

.tool-button:hover:not(.disabled) {
  background: #e8e8e8;
  border-color: #666666;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.tool-button.active {
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
  border-color: #666666;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.tool-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f8f9fa;
}

.tool-icon {
  font-size: 16px;
  display: block;
}

.tool-name {
  font-size: 10px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.toolbar-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 200px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-group label {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 6px;
}

.range-input {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: #dee2e6;
  outline: none;
  -webkit-appearance: none;
}

.range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #2196f3;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-input::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #2196f3;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.color-input {
  width: 40px;
  height: 30px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  background: none;
  padding: 0;
}

.color-input::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-input::-webkit-color-swatch {
  border: none;
  border-radius: 3px;
}

.select-input {
  padding: 6px 8px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
  font-size: 12px;
  cursor: pointer;
}

.select-input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.setting-value {
  font-size: 11px;
  color: #6c757d;
  font-weight: 500;
  margin-top: 2px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 1024px) {
  .image-toolbar {
    flex-direction: column;
    gap: 16px;
    padding: 12px;
  }
  
  .toolbar-section {
    width: 100%;
  }
  
  .tools-grid {
    grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
    max-width: none;
  }
  
  .toolbar-settings {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .image-toolbar {
    padding: 8px;
    gap: 12px;
  }
  
  .tools-grid {
    grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
    gap: 4px;
  }
  
  .tool-button {
    padding: 6px 4px;
    min-width: 40px;
  }
  
  .tool-icon {
    font-size: 14px;
  }
  
  .tool-name {
    font-size: 9px;
  }
  
  .setting-group {
    gap: 6px;
  }
  
  .toolbar-settings {
    gap: 8px;
  }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .image-toolbar {
    background: #2d2d30;
    border-color: #3e3e42;
  }
  
  .section-title {
    color: #cccccc;
  }
  
  .tool-button {
    background: #383838;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .tool-button:hover:not(.disabled) {
    background: #1e3a8a;
    border-color: #2196f3;
  }
  
  .tool-button.disabled {
    background: #2d2d30;
  }
  
  .setting-group label {
    color: #cccccc;
  }
  
  .range-input {
    background: #3e3e42;
  }
  
  .color-input,
  .select-input {
    background: #383838;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .setting-value {
    color: #adb5bd;
  }
}

/* تأثيرات التفاعل */
.tool-button:active:not(.disabled) {
  transform: translateY(0);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.range-input:hover::-webkit-slider-thumb {
  background: #1976d2;
}

.range-input:hover::-moz-range-thumb {
  background: #1976d2;
}

/* تحسينات إمكانية الوصول */
.tool-button:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}

.range-input:focus {
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.3);
}

.color-input:focus {
  outline: 2px solid #2196f3;
  outline-offset: 2px;
}
