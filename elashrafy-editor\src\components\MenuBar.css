.menu-bar {
  display: flex;
  background: linear-gradient(135deg, #f8f8f8 0%, #e8e8e8 100%);
  border-bottom: 2px solid #cccccc;
  padding: 8px 12px;
  gap: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.menu-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.menu-title {
  font-size: 12px;
  font-weight: bold;
  color: #666;
  margin-bottom: 4px;
}

.menu-items {
  display: flex;
  gap: 4px;
  align-items: center;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: none;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #333;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.menu-item:hover:not(.disabled) {
  background-color: #e8e8e8;
  border-color: #666666;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.menu-item:active:not(.disabled) {
  background-color: #d8d8d8;
  transform: translateY(1px);
}

.menu-item.disabled {
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
}

.menu-item .icon {
  font-size: 14px;
  display: inline-block;
  min-width: 16px;
  text-align: center;
}

.separator {
  width: 1px;
  height: 20px;
  background-color: #ddd;
  margin: 0 4px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .menu-bar {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .menu-item {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .menu-item .icon {
    font-size: 12px;
  }
}
