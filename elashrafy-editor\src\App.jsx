import React, { useState, useRef, useCallback, useEffect } from 'react';
import MenuBar from './components/MenuBar';
import TextEditor from './components/TextEditor';
import SearchReplace from './components/SearchReplace';
import StatusBar from './components/StatusBar';
import LightroomEditor from './components/LightroomEditor/LightroomEditor';
import WelcomeScreen from './components/WelcomeScreen';
import { saveEditorState, loadEditorState, isLocalStorageAvailable } from './utils/localStorage';
import './App.css';

function App() {
  // تحميل البيانات المحفوظة مع معالجة الأخطاء
  const savedState = (() => {
    try {
      return loadEditorState();
    } catch (error) {
      console.error('خطأ في تحميل البيانات المحفوظة:', error);
      return {};
    }
  })();

  // حالة النص
  const [content, setContent] = useState(savedState.content || '');
  const [fileName, setFileName] = useState(savedState.fileName || 'مستند جديد');
  const [isModified, setIsModified] = useState(false);

  // تاريخ التراجع والإعادة
  const [history, setHistory] = useState(savedState.history || ['']);
  const [historyIndex, setHistoryIndex] = useState(savedState.historyIndex || 0);

  // حالة البحث والاستبدال
  const [showSearch, setShowSearch] = useState(false);
  const [showReplace, setShowReplace] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [replaceTerm, setReplaceTerm] = useState('');

  // حالة موضع المؤشر
  const [selectionStart, setSelectionStart] = useState(0);
  const [selectionEnd, setSelectionEnd] = useState(0);

  // حالة محرر الصور
  const [showImageEditor, setShowImageEditor] = useState(false);
  const [currentEditingImage, setCurrentEditingImage] = useState(null);

  // حالة الواجهة الترحيبية
  const [showWelcome, setShowWelcome] = useState(true);

  // مراجع للعناصر
  const fileInputRef = useRef(null);
  const textEditorRef = useRef(null);

  // نظام الحفظ التلقائي
  useEffect(() => {
    if (!isLocalStorageAvailable()) return;

    const autoSaveTimer = setInterval(() => {
      if (content || fileName !== 'مستند جديد') {
        saveEditorState(content, fileName, history, historyIndex);
        console.log('تم الحفظ التلقائي:', new Date().toLocaleTimeString());
      }
    }, 30000); // حفظ كل 30 ثانية

    return () => clearInterval(autoSaveTimer);
  }, [content, fileName, history, historyIndex]);

  // إضافة إلى التاريخ
  const addToHistory = useCallback((newContent) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newContent);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [history, historyIndex]);

  // تغيير المحتوى
  const handleContentChange = (newContent) => {
    setContent(newContent);
    setIsModified(true);

    // إضافة إلى التاريخ بعد تأخير قصير لتجنب الإضافة المفرطة
    clearTimeout(window.historyTimeout);
    window.historyTimeout = setTimeout(() => {
      addToHistory(newContent);
      // حفظ فوري عند التغيير
      if (isLocalStorageAvailable()) {
        saveEditorState(newContent, fileName, history, historyIndex);
      }
    }, 500);
  };

  // تتبع موضع المؤشر
  const handleSelectionChange = (start, end) => {
    setSelectionStart(start);
    setSelectionEnd(end);
  };

  // عمليات الملفات
  const handleNew = () => {
    if (isModified) {
      const confirmNew = window.confirm('هناك تغييرات غير محفوظة. هل تريد إنشاء ملف جديد؟');
      if (!confirmNew) return;
    }
    setContent('');
    setFileName('مستند جديد');
    setIsModified(false);
    setHistory(['']);
    setHistoryIndex(0);
    setShowWelcome(false);
  };

  const handleOpen = () => {
    if (isModified) {
      const confirmOpen = window.confirm('هناك تغييرات غير محفوظة. هل تريد فتح ملف جديد؟');
      if (!confirmOpen) return;
    }
    setShowWelcome(false);
    fileInputRef.current?.click();
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const fileContent = e.target.result;
      setContent(fileContent);
      setFileName(file.name);
      setIsModified(false);
      setHistory([fileContent]);
      setHistoryIndex(0);
    };
    reader.readAsText(file);

    // إعادة تعيين قيمة input لتمكين فتح نفس الملف مرة أخرى
    event.target.value = '';
  };

  const handleSave = () => {
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName === 'مستند جديد' ? 'مستند جديد.txt' : fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    setIsModified(false);
  };

  const handleSaveAs = () => {
    const newFileName = prompt('أدخل اسم الملف:', fileName);
    if (newFileName) {
      setFileName(newFileName);
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = newFileName.endsWith('.txt') ? newFileName : newFileName + '.txt';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      setIsModified(false);
    }
  };

  // عمليات التحرير
  const handleUndo = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      setContent(history[newIndex]);
      setIsModified(newIndex !== 0);
    }
  };

  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      setContent(history[newIndex]);
      setIsModified(newIndex !== 0);
    }
  };

  const handleCut = async () => {
    if (textEditorRef.current) {
      const textarea = textEditorRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;

      if (start !== end) {
        const selectedText = content.substring(start, end);
        try {
          await navigator.clipboard.writeText(selectedText);
          const newContent = content.substring(0, start) + content.substring(end);
          setContent(newContent);
          setIsModified(true);
          addToHistory(newContent);

          // تحديث موضع المؤشر
          setTimeout(() => {
            textarea.selectionStart = textarea.selectionEnd = start;
          }, 0);
        } catch (err) {
          console.error('فشل في نسخ النص:', err);
        }
      }
    }
  };

  const handleCopy = async () => {
    if (textEditorRef.current) {
      const textarea = textEditorRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;

      if (start !== end) {
        const selectedText = content.substring(start, end);
        try {
          await navigator.clipboard.writeText(selectedText);
        } catch (err) {
          console.error('فشل في نسخ النص:', err);
        }
      }
    }
  };

  const handlePaste = async () => {
    if (textEditorRef.current) {
      const textarea = textEditorRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;

      try {
        const clipboardText = await navigator.clipboard.readText();
        const newContent = content.substring(0, start) + clipboardText + content.substring(end);
        setContent(newContent);
        setIsModified(true);
        addToHistory(newContent);

        // تحديث موضع المؤشر
        setTimeout(() => {
          const newPosition = start + clipboardText.length;
          textarea.selectionStart = textarea.selectionEnd = newPosition;
        }, 0);
      } catch (err) {
        console.error('فشل في لصق النص:', err);
      }
    }
  };

  // وظائف البحث والاستبدال
  const handleSearch = (searchText, options = {}) => {
    if (!searchText || !textEditorRef.current) return;

    const textarea = textEditorRef.current;
    const text = content;
    const { caseSensitive = false, wholeWord = false, useRegex = false, backward = false } = options;

    let searchRegex;
    try {
      if (useRegex) {
        const flags = caseSensitive ? 'g' : 'gi';
        searchRegex = new RegExp(searchText, flags);
      } else {
        const escapedText = searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const pattern = wholeWord ? `\\b${escapedText}\\b` : escapedText;
        const flags = caseSensitive ? 'g' : 'gi';
        searchRegex = new RegExp(pattern, flags);
      }
    } catch (error) {
      alert('تعبير نمطي غير صحيح');
      return;
    }

    const matches = [...text.matchAll(searchRegex)];
    if (matches.length === 0) {
      alert('لم يتم العثور على النص');
      return;
    }

    const currentPos = textarea.selectionStart;
    let targetMatch;

    if (backward) {
      // البحث للخلف
      const beforeMatches = matches.filter(match => match.index < currentPos);
      targetMatch = beforeMatches.length > 0 ? beforeMatches[beforeMatches.length - 1] : matches[matches.length - 1];
    } else {
      // البحث للأمام
      const afterMatches = matches.filter(match => match.index >= currentPos);
      targetMatch = afterMatches.length > 0 ? afterMatches[0] : matches[0];
    }

    if (targetMatch) {
      textarea.focus();
      textarea.setSelectionRange(targetMatch.index, targetMatch.index + targetMatch[0].length);
    }
  };

  const handleReplace = (searchText, replaceText, options = {}) => {
    if (!searchText || !textEditorRef.current) return;

    const textarea = textEditorRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);

    const { caseSensitive = false, wholeWord = false, useRegex = false } = options;

    let matches = false;
    try {
      if (useRegex) {
        const flags = caseSensitive ? '' : 'i';
        const regex = new RegExp(searchText, flags);
        matches = regex.test(selectedText);
      } else {
        const pattern = wholeWord ? `\\b${searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b` : searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const flags = caseSensitive ? '' : 'i';
        const regex = new RegExp(pattern, flags);
        matches = regex.test(selectedText);
      }
    } catch (error) {
      alert('تعبير نمطي غير صحيح');
      return;
    }

    if (matches) {
      const newContent = content.substring(0, start) + replaceText + content.substring(end);
      setContent(newContent);
      setIsModified(true);
      addToHistory(newContent);

      // تحديد النص المستبدل
      setTimeout(() => {
        textarea.setSelectionRange(start, start + replaceText.length);
      }, 0);
    } else {
      // البحث عن التطابق التالي
      handleSearch(searchText, options);
    }
  };

  const handleReplaceAll = (searchText, replaceText, options = {}) => {
    if (!searchText) return;

    const { caseSensitive = false, wholeWord = false, useRegex = false } = options;

    let searchRegex;
    try {
      if (useRegex) {
        const flags = caseSensitive ? 'g' : 'gi';
        searchRegex = new RegExp(searchText, flags);
      } else {
        const escapedText = searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const pattern = wholeWord ? `\\b${escapedText}\\b` : escapedText;
        const flags = caseSensitive ? 'g' : 'gi';
        searchRegex = new RegExp(pattern, flags);
      }
    } catch (error) {
      alert('تعبير نمطي غير صحيح');
      return;
    }

    const matches = content.match(searchRegex);
    if (!matches) {
      alert('لم يتم العثور على النص');
      return;
    }

    const newContent = content.replace(searchRegex, replaceText);
    setContent(newContent);
    setIsModified(true);
    addToHistory(newContent);

    alert(`تم استبدال ${matches.length} تطابق`);
  };

  // وظائف محرر الصور
  const handleOpenImageEditor = () => {
    setShowWelcome(false);
    setShowImageEditor(true);
  };

  const handleCloseImageEditor = () => {
    setShowImageEditor(false);
    setCurrentEditingImage(null);
  };

  // العودة للصفحة الترحيبية
  const handleBackToWelcome = () => {
    setShowWelcome(true);
    setShowImageEditor(false);
    setCurrentEditingImage(null);
  };

  const handleSaveEditedImage = (imageBlob, imageDataUrl) => {
    console.log('حفظ الصورة المحررة:', imageBlob);
    // إدراج الصورة في النص
    if (textEditorRef.current && imageDataUrl) {
      const textarea = textEditorRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;

      // إنشاء نص HTML للصورة
      const imageMarkdown = `\n![صورة محررة](${imageDataUrl})\n`;
      const newContent = content.substring(0, start) + imageMarkdown + content.substring(end);

      setContent(newContent);
      setIsModified(true);
      addToHistory(newContent);

      // إغلاق محرر الصور
      handleCloseImageEditor();

      // تحديث موضع المؤشر
      setTimeout(() => {
        const newPosition = start + imageMarkdown.length;
        textarea.selectionStart = textarea.selectionEnd = newPosition;
        textarea.focus();
      }, 100);
    }
  };

  // معالجة لصق الصور من الحافظة
  const handlePasteImage = useCallback(async (e) => {
    const items = e.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.startsWith('image/')) {
        e.preventDefault();
        const file = item.getAsFile();
        if (file) {
          const reader = new FileReader();
          reader.onload = (event) => {
            setCurrentEditingImage(event.target.result);
            setShowImageEditor(true);
          };
          reader.readAsDataURL(file);
        }
        break;
      }
    }
  }, []);

  // دعم اختصارات لوحة المفاتيح
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey) {
        switch (e.key) {
          case 'n':
            e.preventDefault();
            handleNew();
            break;
          case 'o':
            e.preventDefault();
            handleOpen();
            break;
          case 's':
            e.preventDefault();
            if (e.shiftKey) {
              handleSaveAs();
            } else {
              handleSave();
            }
            break;
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              handleRedo();
            } else {
              handleUndo();
            }
            break;
          case 'y':
            e.preventDefault();
            handleRedo();
            break;
          case 'f':
            e.preventDefault();
            setShowSearch(true);
            break;
          case 'h':
            e.preventDefault();
            setShowReplace(true);
            break;
          case 'i':
            e.preventDefault();
            handleOpenImageEditor();
            break;
          default:
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [content, isModified, historyIndex, history.length]);

  return (
    <div className="app">
      {showWelcome && (
        <WelcomeScreen
          onNewFile={handleNew}
          onOpenFile={handleOpen}
          onOpenImageEditor={handleOpenImageEditor}
          onClose={() => setShowWelcome(false)}
        />
      )}

      <div className="app-header">
        <div className="header-left">
          <button
            className="back-to-welcome-btn"
            onClick={handleBackToWelcome}
            title="العودة للصفحة الرئيسية"
          >
            🏠
          </button>
        </div>
        <h1 className="app-title">
          <span className="app-logo">📝</span>
          Elashrafy Editor - {fileName}{isModified ? ' *' : ''}
        </h1>
        <div className="header-right"></div>
      </div>

      <MenuBar
        onNew={handleNew}
        onOpen={handleOpen}
        onSave={handleSave}
        onSaveAs={handleSaveAs}
        onCut={handleCut}
        onCopy={handleCopy}
        onPaste={handlePaste}
        onUndo={handleUndo}
        onRedo={handleRedo}
        onFind={() => setShowSearch(true)}
        onReplace={() => setShowReplace(true)}
        onOpenImageEditor={handleOpenImageEditor}
        canUndo={historyIndex > 0}
        canRedo={historyIndex < history.length - 1}
      />

      <div className="editor-container">
        <TextEditor
          ref={textEditorRef}
          content={content}
          onChange={handleContentChange}
          onSelectionChange={handleSelectionChange}
          onPaste={handlePasteImage}
          searchTerm={searchTerm}
        />
      </div>

      <SearchReplace
        isOpen={showSearch}
        onClose={() => setShowSearch(false)}
        onSearch={handleSearch}
        onReplace={handleReplace}
        onReplaceAll={handleReplaceAll}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        replaceTerm={replaceTerm}
        setReplaceTerm={setReplaceTerm}
        showReplace={false}
      />

      <SearchReplace
        isOpen={showReplace}
        onClose={() => setShowReplace(false)}
        onSearch={handleSearch}
        onReplace={handleReplace}
        onReplaceAll={handleReplaceAll}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        replaceTerm={replaceTerm}
        setReplaceTerm={setReplaceTerm}
        showReplace={true}
      />

      <StatusBar
        content={content}
        fileName={fileName}
        isModified={isModified}
        selectionStart={selectionStart}
        selectionEnd={selectionEnd}
      />

      <LightroomEditor
        isOpen={showImageEditor}
        onClose={handleCloseImageEditor}
        onSave={handleSaveEditedImage}
        onBackToWelcome={handleBackToWelcome}
      />

      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        accept=".txt,.md,.js,.jsx,.html,.css,.json"
        onChange={handleFileSelect}
      />
    </div>
  );
}

export default App;
