.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 4px 16px;
  font-size: 12px;
  color: #6c757d;
  font-family: 'Courier New', monospace;
  direction: rtl;
  min-height: 24px;
}

.status-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.status-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.status-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  flex: 1;
}

.file-info {
  font-weight: 500;
  color: #495057;
}

.selection-info {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.cursor-info,
.document-info,
.encoding-info {
  white-space: nowrap;
}

.separator {
  color: #adb5bd;
  margin: 0 4px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .status-bar {
    flex-direction: column;
    gap: 4px;
    padding: 6px 12px;
  }
  
  .status-left,
  .status-center,
  .status-right {
    flex: none;
    width: 100%;
    justify-content: center;
  }
  
  .status-right {
    flex-direction: column;
    gap: 2px;
  }
  
  .separator {
    display: none;
  }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .status-bar {
    background-color: #2d2d30;
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .file-info {
    color: #cccccc;
  }
  
  .selection-info {
    background-color: #1e3a8a;
    color: #93c5fd;
  }
  
  .separator {
    color: #6c757d;
  }
}
