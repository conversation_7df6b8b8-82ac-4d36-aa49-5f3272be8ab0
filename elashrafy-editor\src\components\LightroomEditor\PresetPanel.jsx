import { useState } from 'react';
import './PresetPanel.css';

const PresetPanel = ({ onApplyPreset }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const presets = [
    {
      name: 'طبيعي',
      category: 'أساسي',
      thumbnail: '🌿',
      adjustments: {
        exposure: 0.2, highlights: -20, shadows: 30, whites: 10, blacks: -10,
        brightness: 5, contrast: 15, saturation: 10, vibrance: 15,
        temperature: 5, tint: 0,
        hue: { red: 0, orange: 0, yellow: 0, green: 5, aqua: 0, blue: 0, purple: 0, magenta: 0 },
        saturationHSL: { red: 0, orange: 0, yellow: 0, green: 15, aqua: 0, blue: 0, purple: 0, magenta: 0 },
        luminance: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
        clarity: 10, dehaze: 5, noiseReduction: 0, sharpening: 25,
        cropX: 0, cropY: 0, cropWidth: 100, cropHeight: 100,
        rotation: 0, flipHorizontal: false, flipVertical: false
      }
    },
    {
      name: 'دراماتيكي',
      category: 'فني',
      thumbnail: '🎭',
      adjustments: {
        exposure: -0.3, highlights: -60, shadows: 40, whites: -20, blacks: -30,
        brightness: -10, contrast: 40, saturation: 20, vibrance: 30,
        temperature: -10, tint: 5,
        hue: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
        saturationHSL: { red: 10, orange: 5, yellow: 0, green: 0, aqua: 0, blue: 10, purple: 0, magenta: 0 },
        luminance: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: -10, purple: 0, magenta: 0 },
        clarity: 40, dehaze: 20, noiseReduction: 0, sharpening: 40,
        cropX: 0, cropY: 0, cropWidth: 100, cropHeight: 100,
        rotation: 0, flipHorizontal: false, flipVertical: false
      }
    },
    {
      name: 'دافئ',
      category: 'مزاج',
      thumbnail: '🌅',
      adjustments: {
        exposure: 0.3, highlights: -30, shadows: 20, whites: 15, blacks: -5,
        brightness: 10, contrast: 10, saturation: 15, vibrance: 25,
        temperature: 25, tint: 5,
        hue: { red: 5, orange: 10, yellow: 5, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
        saturationHSL: { red: 15, orange: 25, yellow: 20, green: 0, aqua: -10, blue: -15, purple: 0, magenta: 0 },
        luminance: { red: 5, orange: 10, yellow: 5, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
        clarity: 15, dehaze: 0, noiseReduction: 0, sharpening: 30,
        cropX: 0, cropY: 0, cropWidth: 100, cropHeight: 100,
        rotation: 0, flipHorizontal: false, flipVertical: false
      }
    },
    {
      name: 'بارد',
      category: 'مزاج',
      thumbnail: '❄️',
      adjustments: {
        exposure: 0.1, highlights: -20, shadows: 15, whites: 5, blacks: -15,
        brightness: 0, contrast: 20, saturation: 5, vibrance: 10,
        temperature: -20, tint: -5,
        hue: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 5, blue: 10, purple: 0, magenta: 0 },
        saturationHSL: { red: -10, orange: -15, yellow: -10, green: 5, aqua: 15, blue: 20, purple: 0, magenta: 0 },
        luminance: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 5, blue: 0, purple: 0, magenta: 0 },
        clarity: 20, dehaze: 10, noiseReduction: 0, sharpening: 35,
        cropX: 0, cropY: 0, cropWidth: 100, cropHeight: 100,
        rotation: 0, flipHorizontal: false, flipVertical: false
      }
    },
    {
      name: 'أبيض وأسود',
      category: 'كلاسيكي',
      thumbnail: '⚫',
      adjustments: {
        exposure: 0.2, highlights: -40, shadows: 30, whites: 20, blacks: -25,
        brightness: 5, contrast: 30, saturation: -100, vibrance: 0,
        temperature: 0, tint: 0,
        hue: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
        saturationHSL: { red: -100, orange: -100, yellow: -100, green: -100, aqua: -100, blue: -100, purple: -100, magenta: -100 },
        luminance: { red: 0, orange: 0, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
        clarity: 25, dehaze: 15, noiseReduction: 0, sharpening: 45,
        cropX: 0, cropY: 0, cropWidth: 100, cropHeight: 100,
        rotation: 0, flipHorizontal: false, flipVertical: false
      }
    },
    {
      name: 'فينتاج',
      category: 'كلاسيكي',
      thumbnail: '📷',
      adjustments: {
        exposure: 0.1, highlights: -50, shadows: 40, whites: -10, blacks: 10,
        brightness: 5, contrast: -10, saturation: -20, vibrance: 5,
        temperature: 15, tint: 10,
        hue: { red: 10, orange: 15, yellow: 10, green: 0, aqua: 0, blue: -5, purple: 0, magenta: 0 },
        saturationHSL: { red: -10, orange: 10, yellow: 15, green: -5, aqua: -10, blue: -15, purple: 0, magenta: 0 },
        luminance: { red: 5, orange: 10, yellow: 5, green: 0, aqua: 0, blue: -5, purple: 0, magenta: 0 },
        clarity: -15, dehaze: -10, noiseReduction: 15, sharpening: 20,
        cropX: 0, cropY: 0, cropWidth: 100, cropHeight: 100,
        rotation: 0, flipHorizontal: false, flipVertical: false
      }
    },
    {
      name: 'سينمائي',
      category: 'فني',
      thumbnail: '🎬',
      adjustments: {
        exposure: -0.2, highlights: -70, shadows: 50, whites: -30, blacks: 20,
        brightness: -5, contrast: 25, saturation: -10, vibrance: 20,
        temperature: -5, tint: 0,
        hue: { red: 0, orange: 5, yellow: 10, green: 0, aqua: 0, blue: -10, purple: 0, magenta: 0 },
        saturationHSL: { red: 5, orange: 10, yellow: 15, green: -5, aqua: -10, blue: 0, purple: 0, magenta: 0 },
        luminance: { red: 0, orange: 5, yellow: 0, green: 0, aqua: 0, blue: -15, purple: 0, magenta: 0 },
        clarity: 30, dehaze: 25, noiseReduction: 0, sharpening: 50,
        cropX: 0, cropY: 0, cropWidth: 100, cropHeight: 56.25,
        rotation: 0, flipHorizontal: false, flipVertical: false
      }
    },
    {
      name: 'صورة شخصية',
      category: 'أساسي',
      thumbnail: '👤',
      adjustments: {
        exposure: 0.3, highlights: -30, shadows: 25, whites: 10, blacks: -5,
        brightness: 8, contrast: 5, saturation: 5, vibrance: 15,
        temperature: 10, tint: 2,
        hue: { red: 0, orange: 5, yellow: 0, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
        saturationHSL: { red: 5, orange: 10, yellow: 5, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
        luminance: { red: 5, orange: 8, yellow: 3, green: 0, aqua: 0, blue: 0, purple: 0, magenta: 0 },
        clarity: -10, dehaze: 0, noiseReduction: 10, sharpening: 25,
        cropX: 0, cropY: 0, cropWidth: 100, cropHeight: 100,
        rotation: 0, flipHorizontal: false, flipVertical: false
      }
    }
  ];

  const categories = [...new Set(presets.map(preset => preset.category))];

  return (
    <div className={`preset-panel ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="preset-header" onClick={() => setIsCollapsed(!isCollapsed)}>
        <h3 className="preset-title">الإعدادات المسبقة</h3>
        <button className={`collapse-btn ${isCollapsed ? 'collapsed' : ''}`}>
          ▼
        </button>
      </div>

      {!isCollapsed && (
        <div className="preset-content">
          {categories.map(category => (
            <div key={category} className="preset-category">
              <div className="category-title">{category}</div>
              <div className="preset-grid">
                {presets
                  .filter(preset => preset.category === category)
                  .map((preset, index) => (
                    <button
                      key={index}
                      className="preset-item"
                      onClick={() => {
                        console.log('تطبيق إعداد:', preset.name);
                        onApplyPreset(preset.adjustments);
                      }}
                      title={`تطبيق إعداد ${preset.name}`}
                    >
                      <div className="preset-thumbnail">
                        {preset.thumbnail}
                      </div>
                      <div className="preset-name">{preset.name}</div>
                    </button>
                  ))}
              </div>
            </div>
          ))}

          <div className="preset-actions">
            <button className="action-btn" title="حفظ إعداد مخصص">
              💾 حفظ
            </button>
            <button className="action-btn" title="استيراد إعدادات">
              📥 استيراد
            </button>
            <button className="action-btn" title="تصدير إعدادات">
              📤 تصدير
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PresetPanel;
