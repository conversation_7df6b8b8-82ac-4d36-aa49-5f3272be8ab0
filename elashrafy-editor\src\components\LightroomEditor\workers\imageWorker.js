// Web Worker لمعالجة الصور في الخلفية

// دوال مساعدة لتحويل الألوان
function rgbToHsl(r, g, b) {
  r /= 255;
  g /= 255;
  b /= 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h, s, l = (max + min) / 2;
  
  if (max === min) {
    h = s = 0;
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }
  
  return [h, s, l];
}

function hslToRgb(h, s, l) {
  let r, g, b;
  
  if (s === 0) {
    r = g = b = l;
  } else {
    const hue2rgb = (p, q, t) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };
    
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }
  
  return [r * 255, g * 255, b * 255];
}

// معالجة البيانات
function processImageData(imageData, adjustments, onProgress) {
  const data = imageData.data;
  const length = data.length;
  const totalPixels = length / 4;
  let processedPixels = 0;
  const progressInterval = Math.floor(totalPixels / 20); // تحديث كل 5%
  
  for (let i = 0; i < length; i += 4) {
    let r = data[i];
    let g = data[i + 1];
    let b = data[i + 2];
    const a = data[i + 3];
    
    // تخطي البكسلات الشفافة
    if (a === 0) continue;
    
    // تطبيق التعرض
    if (adjustments.exposure && adjustments.exposure !== 0) {
      const exposureFactor = Math.pow(2, adjustments.exposure);
      r *= exposureFactor;
      g *= exposureFactor;
      b *= exposureFactor;
    }
    
    // تطبيق السطوع
    if (adjustments.brightness && adjustments.brightness !== 0) {
      const brightnessFactor = adjustments.brightness * 2.55;
      r += brightnessFactor;
      g += brightnessFactor;
      b += brightnessFactor;
    }
    
    // تطبيق التباين
    if (adjustments.contrast && adjustments.contrast !== 0) {
      const contrastFactor = (259 * (adjustments.contrast + 255)) / (255 * (259 - adjustments.contrast));
      r = contrastFactor * (r - 128) + 128;
      g = contrastFactor * (g - 128) + 128;
      b = contrastFactor * (b - 128) + 128;
    }
    
    // تطبيق الإضاءات والظلال
    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
    
    if (adjustments.highlights && adjustments.highlights !== 0) {
      const highlightStrength = adjustments.highlights / 100;
      if (luminance > 128) {
        const mask = Math.pow((luminance - 128) / 127, 0.5);
        const factor = 1 + (highlightStrength * mask);
        r *= factor;
        g *= factor;
        b *= factor;
      }
    }
    
    if (adjustments.shadows && adjustments.shadows !== 0) {
      const shadowStrength = adjustments.shadows / 100;
      if (luminance < 128) {
        const mask = Math.pow((128 - luminance) / 128, 0.5);
        const factor = 1 + (shadowStrength * mask);
        r *= factor;
        g *= factor;
        b *= factor;
      }
    }
    
    // تطبيق النقاط البيضاء والسوداء
    if (adjustments.whites && adjustments.whites !== 0) {
      const whitesFactor = adjustments.whites / 100;
      if (luminance > 192) {
        const mask = (luminance - 192) / 63;
        const factor = 1 + (whitesFactor * mask);
        r *= factor;
        g *= factor;
        b *= factor;
      }
    }
    
    if (adjustments.blacks && adjustments.blacks !== 0) {
      const blacksFactor = adjustments.blacks / 100;
      if (luminance < 64) {
        const mask = (64 - luminance) / 64;
        const factor = 1 + (blacksFactor * mask);
        r *= factor;
        g *= factor;
        b *= factor;
      }
    }
    
    // تطبيق توازن الأبيض
    if (adjustments.temperature && adjustments.temperature !== 0) {
      const tempStrength = adjustments.temperature / 100;
      if (tempStrength > 0) {
        r += (tempStrength * 40 * (r / 255));
        b -= (tempStrength * 25 * (b / 255));
      } else {
        r += (tempStrength * 25 * (r / 255));
        b -= (tempStrength * 40 * (b / 255));
      }
    }
    
    if (adjustments.tint && adjustments.tint !== 0) {
      const tintStrength = adjustments.tint / 100;
      if (tintStrength > 0) {
        g += (tintStrength * 30 * (g / 255));
      } else {
        r -= (tintStrength * 15 * (r / 255));
        b -= (tintStrength * 15 * (b / 255));
      }
    }
    
    // تطبيق التشبع والحيوية
    if ((adjustments.saturation && adjustments.saturation !== 0) || 
        (adjustments.vibrance && adjustments.vibrance !== 0)) {
      const [h, s, l] = rgbToHsl(r, g, b);
      
      let newS = s;
      
      if (adjustments.saturation && adjustments.saturation !== 0) {
        const saturationFactor = adjustments.saturation / 100;
        newS = s * (1 + saturationFactor);
      }
      
      if (adjustments.vibrance && adjustments.vibrance !== 0) {
        const vibranceFactor = adjustments.vibrance / 100;
        const vibranceBoost = (1 - s) * vibranceFactor;
        newS += vibranceBoost;
      }
      
      newS = Math.max(0, Math.min(1, newS));
      const [newR, newG, newB] = hslToRgb(h, newS, l);
      r = newR;
      g = newG;
      b = newB;
    }
    
    // تطبيق الوضوح
    if (adjustments.clarity && adjustments.clarity !== 0) {
      const clarityFactor = adjustments.clarity / 100;
      const avg = (r + g + b) / 3;
      r = avg + (r - avg) * (1 + clarityFactor);
      g = avg + (g - avg) * (1 + clarityFactor);
      b = avg + (b - avg) * (1 + clarityFactor);
    }
    
    // تطبيق إزالة الضباب
    if (adjustments.dehaze && adjustments.dehaze !== 0) {
      const dehazeFactor = 1 + (adjustments.dehaze / 100);
      r *= dehazeFactor;
      g *= dehazeFactor;
      b *= dehazeFactor;
    }
    
    // تحديد القيم في النطاق الصحيح
    data[i] = Math.max(0, Math.min(255, r));
    data[i + 1] = Math.max(0, Math.min(255, g));
    data[i + 2] = Math.max(0, Math.min(255, b));
    
    // تحديث مؤشر التقدم
    processedPixels++;
    if (processedPixels % progressInterval === 0) {
      const progress = (processedPixels / totalPixels) * 100;
      onProgress(progress);
    }
  }
  
  return imageData;
}

// معالجة الرسائل من الخيط الرئيسي
self.onmessage = function(e) {
  const { type, imageData, adjustments, id } = e.data;
  
  if (type === 'PROCESS_IMAGE') {
    try {
      const processedData = processImageData(
        imageData, 
        adjustments, 
        (progress) => {
          self.postMessage({
            type: 'PROGRESS',
            progress,
            id
          });
        }
      );
      
      self.postMessage({
        type: 'COMPLETE',
        imageData: processedData,
        id
      });
    } catch (error) {
      self.postMessage({
        type: 'ERROR',
        error: error.message,
        id
      });
    }
  }
};
