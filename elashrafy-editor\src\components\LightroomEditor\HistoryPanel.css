.history-panel {
  background: #1a1a1a;
  border-bottom: 1px solid #333;
  transition: all 0.3s ease;
}

.history-panel.collapsed .history-content {
  display: none;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #2a2a2a;
  cursor: pointer;
  user-select: none;
  transition: background 0.2s ease;
}

.history-header:hover {
  background: #333;
}

.history-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 6px;
}

.history-count {
  font-size: 12px;
  color: #4a90e2;
  font-weight: 400;
}

.collapse-btn {
  background: none;
  border: none;
  color: #ccc;
  font-size: 12px;
  cursor: pointer;
  transition: transform 0.3s ease;
  padding: 4px;
}

.collapse-btn.collapsed {
  transform: rotate(-90deg);
}

.history-content {
  max-height: 300px;
  overflow-y: auto;
}

.empty-history {
  padding: 24px 16px;
  text-align: center;
  color: #666;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.empty-history p {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #888;
}

.empty-history small {
  font-size: 12px;
  color: #666;
}

.history-list {
  padding: 8px 0;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  position: relative;
}

.history-item:hover {
  background: #2a2a2a;
}

.history-item.active {
  background: #333;
  border-left-color: #4a90e2;
}

.history-item.future {
  opacity: 0.5;
}

.history-item.future:hover {
  opacity: 0.8;
}

.history-icon {
  font-size: 16px;
  margin-left: 8px;
  width: 20px;
  text-align: center;
}

.history-details {
  flex: 1;
  min-width: 0;
}

.history-action {
  font-size: 13px;
  color: #ffffff;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-time {
  font-size: 11px;
  color: #888;
  margin-top: 2px;
}

.history-indicator {
  width: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.current-indicator {
  width: 8px;
  height: 8px;
  background: #4a90e2;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(74, 144, 226, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
  }
}

.history-actions {
  padding: 12px 16px;
  border-top: 1px solid #333;
  background: #1a1a1a;
}

.history-info {
  margin-bottom: 8px;
  text-align: center;
}

.current-step {
  font-size: 12px;
  color: #888;
}

.history-controls {
  display: flex;
  justify-content: center;
  gap: 4px;
}

.history-btn {
  background: #333;
  border: 1px solid #555;
  color: #cccccc;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 28px;
}

.history-btn:hover:not(:disabled) {
  background: #444;
  color: #ffffff;
  border-color: #666;
}

.history-btn:active:not(:disabled) {
  background: #555;
}

.history-btn:disabled {
  background: #222;
  color: #555;
  border-color: #333;
  cursor: not-allowed;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .history-content {
    max-height: 250px;
  }
  
  .history-item {
    padding: 6px 12px;
  }
  
  .history-icon {
    font-size: 14px;
    margin-left: 6px;
    width: 18px;
  }
  
  .history-action {
    font-size: 12px;
  }
  
  .history-time {
    font-size: 10px;
  }
  
  .history-actions {
    padding: 10px 12px;
  }
  
  .history-btn {
    padding: 4px 6px;
    min-width: 28px;
    height: 24px;
    font-size: 11px;
  }
  
  .empty-history {
    padding: 20px 12px;
  }
  
  .empty-icon {
    font-size: 28px;
  }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1920px) {
  .history-content {
    max-height: 400px;
  }
  
  .history-item {
    padding: 10px 20px;
  }
  
  .history-icon {
    font-size: 18px;
    margin-left: 10px;
    width: 24px;
  }
  
  .history-action {
    font-size: 14px;
  }
  
  .history-time {
    font-size: 12px;
  }
  
  .history-actions {
    padding: 16px 20px;
  }
  
  .history-btn {
    padding: 8px 10px;
    min-width: 36px;
    height: 32px;
    font-size: 13px;
  }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
  .history-panel {
    border-bottom-color: #ffffff;
  }
  
  .history-item {
    border-left-width: 4px;
  }
  
  .history-item.active {
    border-left-color: #ffffff;
    background: #000000;
  }
  
  .history-btn {
    border-color: #ffffff;
  }
  
  .history-actions {
    border-top-color: #ffffff;
  }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  .history-item {
    transition: none;
  }
  
  .collapse-btn {
    transition: none;
  }
  
  .current-indicator {
    animation: none;
  }
  
  .history-btn {
    transition: none;
  }
}

/* تحسينات إمكانية الوصول */
.history-item:focus {
  outline: 2px solid #4a90e2;
  outline-offset: -2px;
}

.history-btn:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

.history-header:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

/* شريط التمرير المخصص */
.history-content::-webkit-scrollbar {
  width: 6px;
}

.history-content::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.history-content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 3px;
}

.history-content::-webkit-scrollbar-thumb:hover {
  background: #666;
}
