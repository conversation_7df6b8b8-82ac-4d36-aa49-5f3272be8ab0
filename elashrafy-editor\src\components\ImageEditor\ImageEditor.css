.image-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  direction: rtl;
}

.image-editor {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  width: 95vw;
  height: 90vh;
  max-width: 1400px;
  max-height: 900px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.image-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  border-bottom: 3px solid #666666;
}

.image-editor-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions button {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.header-actions button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.header-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.crop-apply-btn {
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%) !important;
  border: 2px solid #666666 !important;
  animation: pulse 2s infinite;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
  100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
}

.image-editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.image-editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
  min-height: 0;
  justify-content: center;
  align-items: center;
}

.canvas-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f8f9fa;
  position: relative;
  overflow: auto;
  padding: 20px;
  min-width: 0;
  max-width: 100%;
  max-height: 100%;
}

/* تم نقل اللوحة الجانبية إلى ImageSidebar */

/* تحسينات للشاشات الصغيرة */
@media (max-width: 1024px) {
  .image-editor {
    width: 98vw;
    height: 95vh;
  }
  
  .image-editor-main {
    flex-direction: column;
  }
  
  /* تم نقل اللوحة الجانبية إلى ImageSidebar */
  
  .canvas-container {
    flex: 1;
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .image-editor-header {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
  }
  
  .image-editor-header h2 {
    font-size: 18px;
  }
  
  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .header-actions button {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .canvas-container {
    padding: 10px;
  }
  
  .properties-panel {
    height: 200px;
  }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .image-editor {
    background: #2d2d30;
    color: #cccccc;
  }
  
  .canvas-container {
    background: #1e1e1e;
  }
  
  /* تم نقل اللوحة الجانبية إلى ImageSidebar */
}

/* تأثيرات التحميل */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* تحسينات الأداء */
.image-editor * {
  box-sizing: border-box;
}

.image-editor canvas {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* تأثيرات السحب والإفلات */
.canvas-container.drag-over {
  background: #e3f2fd;
  border: 2px dashed #2196f3;
}

.canvas-container.drag-over::after {
  content: 'اسحب الصورة هنا';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  color: #2196f3;
  font-weight: 600;
  pointer-events: none;
}
