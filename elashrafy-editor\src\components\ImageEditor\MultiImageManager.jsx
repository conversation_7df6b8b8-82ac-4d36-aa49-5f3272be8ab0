import React, { useState, useRef } from 'react';
import './MultiImageManager.css';

const MultiImageManager = ({ 
  images, 
  currentImageIndex, 
  onImageSelect, 
  onImageAdd, 
  onImageRemove,
  onImageExport 
}) => {
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [exportSettings, setExportSettings] = useState({
    format: 'png',
    quality: 90,
    width: '',
    height: '',
    maintainAspectRatio: true
  });
  const fileInputRef = useRef(null);

  // أنواع الملفات المدعومة
  const supportedFormats = [
    { value: 'png', label: 'PNG', description: 'جودة عالية مع شفافية' },
    { value: 'jpeg', label: 'JPEG', description: 'حجم أصغر للصور' },
    { value: 'webp', label: 'WebP', description: 'تقنية حديثة وحجم صغير' },
    { value: 'gif', label: 'GIF', description: 'للصور المتحركة' },
    { value: 'bmp', label: 'BMP', description: 'تنسيق غير مضغوط' }
  ];

  // معالجة تحميل صور متعددة
  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const img = new Image();
          img.onload = () => {
            onImageAdd({
              id: Date.now() + Math.random(),
              name: file.name,
              src: e.target.result,
              width: img.width,
              height: img.height,
              size: file.size,
              type: file.type,
              lastModified: new Date()
            });
          };
          img.src = e.target.result;
        };
        reader.readAsDataURL(file);
      }
    });
    event.target.value = '';
  };

  // تصدير الصورة الحالية
  const handleExport = () => {
    if (!images[currentImageIndex]) return;
    
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // تحديد الأبعاد
      let { width, height } = exportSettings;
      if (!width && !height) {
        width = img.width;
        height = img.height;
      } else if (!width) {
        width = (height * img.width) / img.height;
      } else if (!height) {
        height = (width * img.height) / img.width;
      } else if (exportSettings.maintainAspectRatio) {
        const aspectRatio = img.width / img.height;
        if (width / height > aspectRatio) {
          width = height * aspectRatio;
        } else {
          height = width / aspectRatio;
        }
      }
      
      canvas.width = width;
      canvas.height = height;
      ctx.drawImage(img, 0, 0, width, height);
      
      // تصدير بالتنسيق المحدد
      const mimeType = `image/${exportSettings.format}`;
      const quality = exportSettings.format === 'jpeg' ? exportSettings.quality / 100 : undefined;
      
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${images[currentImageIndex].name.split('.')[0]}.${exportSettings.format}`;
        a.click();
        URL.revokeObjectURL(url);
        setShowExportDialog(false);
      }, mimeType, quality);
    };
    
    img.src = images[currentImageIndex].src;
  };

  // تنسيق حجم الملف
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="multi-image-manager">
      {/* شريط الأدوات */}
      <div className="image-toolbar">
        <button 
          className="add-image-btn"
          onClick={() => fileInputRef.current?.click()}
          title="إضافة صور"
        >
          <span className="btn-icon">📁</span>
          إضافة صور
        </button>
        
        <button 
          className="export-btn"
          onClick={() => setShowExportDialog(true)}
          disabled={!images[currentImageIndex]}
          title="تصدير الصورة"
        >
          <span className="btn-icon">💾</span>
          تصدير
        </button>
        
        <div className="image-counter">
          {images.length > 0 ? `${currentImageIndex + 1} من ${images.length}` : 'لا توجد صور'}
        </div>
      </div>

      {/* قائمة الصور */}
      <div className="images-list">
        {images.map((image, index) => (
          <div 
            key={image.id}
            className={`image-item ${index === currentImageIndex ? 'active' : ''}`}
            onClick={() => onImageSelect(index)}
          >
            <div className="image-preview">
              <img src={image.src} alt={image.name} />
            </div>
            <div className="image-info">
              <div className="image-name" title={image.name}>
                {image.name}
              </div>
              <div className="image-details">
                {image.width} × {image.height} • {formatFileSize(image.size)}
              </div>
            </div>
            <button 
              className="remove-btn"
              onClick={(e) => {
                e.stopPropagation();
                onImageRemove(index);
              }}
              title="حذف الصورة"
            >
              ✕
            </button>
          </div>
        ))}
      </div>

      {/* مربع حوار التصدير */}
      {showExportDialog && (
        <div className="export-dialog-overlay">
          <div className="export-dialog">
            <div className="dialog-header">
              <h3>تصدير الصورة</h3>
              <button 
                className="close-btn"
                onClick={() => setShowExportDialog(false)}
              >
                ✕
              </button>
            </div>
            
            <div className="dialog-content">
              <div className="setting-group">
                <label>تنسيق الملف:</label>
                <select 
                  value={exportSettings.format}
                  onChange={(e) => setExportSettings({...exportSettings, format: e.target.value})}
                >
                  {supportedFormats.map(format => (
                    <option key={format.value} value={format.value}>
                      {format.label} - {format.description}
                    </option>
                  ))}
                </select>
              </div>
              
              {exportSettings.format === 'jpeg' && (
                <div className="setting-group">
                  <label>الجودة: {exportSettings.quality}%</label>
                  <input 
                    type="range"
                    min="1"
                    max="100"
                    value={exportSettings.quality}
                    onChange={(e) => setExportSettings({...exportSettings, quality: parseInt(e.target.value)})}
                  />
                </div>
              )}
              
              <div className="setting-group">
                <label>الأبعاد (بكسل):</label>
                <div className="dimensions-inputs">
                  <input 
                    type="number"
                    placeholder="العرض"
                    value={exportSettings.width}
                    onChange={(e) => setExportSettings({...exportSettings, width: e.target.value})}
                  />
                  <span>×</span>
                  <input 
                    type="number"
                    placeholder="الارتفاع"
                    value={exportSettings.height}
                    onChange={(e) => setExportSettings({...exportSettings, height: e.target.value})}
                  />
                </div>
                <label className="checkbox-label">
                  <input 
                    type="checkbox"
                    checked={exportSettings.maintainAspectRatio}
                    onChange={(e) => setExportSettings({...exportSettings, maintainAspectRatio: e.target.checked})}
                  />
                  الحفاظ على نسبة العرض إلى الارتفاع
                </label>
              </div>
            </div>
            
            <div className="dialog-actions">
              <button 
                className="cancel-btn"
                onClick={() => setShowExportDialog(false)}
              >
                إلغاء
              </button>
              <button 
                className="export-btn primary"
                onClick={handleExport}
              >
                تصدير
              </button>
            </div>
          </div>
        </div>
      )}

      {/* مدخل الملفات المخفي */}
      <input 
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />
    </div>
  );
};

export default MultiImageManager;
