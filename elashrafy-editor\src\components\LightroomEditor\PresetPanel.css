.preset-panel {
  background: #1a1a1a;
  border-bottom: 1px solid #333;
  transition: all 0.3s ease;
}

.preset-panel.collapsed .preset-content {
  display: none;
}

.preset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #2a2a2a;
  cursor: pointer;
  user-select: none;
  transition: background 0.2s ease;
}

.preset-header:hover {
  background: #333;
}

.preset-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.collapse-btn {
  background: none;
  border: none;
  color: #ccc;
  font-size: 12px;
  cursor: pointer;
  transition: transform 0.3s ease;
  padding: 4px;
}

.collapse-btn.collapsed {
  transform: rotate(-90deg);
}

.preset-content {
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.preset-category {
  margin-bottom: 20px;
}

.category-title {
  font-size: 12px;
  font-weight: 600;
  color: #4a90e2;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #333;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
  gap: 8px;
}

.preset-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 4px;
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.preset-item:hover {
  background: #333;
  border-color: #4a90e2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.preset-item:active {
  transform: translateY(0);
}

.preset-thumbnail {
  font-size: 24px;
  margin-bottom: 4px;
  filter: grayscale(0.3);
  transition: filter 0.2s ease;
}

.preset-item:hover .preset-thumbnail {
  filter: grayscale(0);
}

.preset-name {
  font-size: 10px;
  color: #cccccc;
  font-weight: 500;
  line-height: 1.2;
  word-break: break-word;
}

.preset-item:hover .preset-name {
  color: #ffffff;
}

.preset-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #333;
}

.action-btn {
  flex: 1;
  padding: 8px 12px;
  background: #333;
  border: 1px solid #555;
  color: #cccccc;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  font-family: inherit;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.action-btn:hover {
  background: #444;
  color: #ffffff;
  border-color: #666;
}

.action-btn:active {
  background: #555;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .preset-content {
    padding: 12px;
    max-height: 250px;
  }
  
  .preset-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 6px;
  }
  
  .preset-item {
    padding: 6px 2px;
  }
  
  .preset-thumbnail {
    font-size: 20px;
    margin-bottom: 2px;
  }
  
  .preset-name {
    font-size: 9px;
  }
  
  .preset-actions {
    flex-direction: column;
    gap: 6px;
  }
  
  .action-btn {
    padding: 6px 8px;
    font-size: 10px;
  }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1920px) {
  .preset-content {
    padding: 20px;
    max-height: 400px;
  }
  
  .preset-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
  }
  
  .preset-item {
    padding: 10px 6px;
  }
  
  .preset-thumbnail {
    font-size: 28px;
    margin-bottom: 6px;
  }
  
  .preset-name {
    font-size: 11px;
  }
  
  .action-btn {
    padding: 10px 14px;
    font-size: 12px;
  }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
  .preset-panel {
    border-bottom-color: #ffffff;
  }
  
  .preset-item {
    border-color: #ffffff;
  }
  
  .preset-item:hover {
    border-color: #4a90e2;
    background: #000000;
  }
  
  .action-btn {
    border-color: #ffffff;
  }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  .preset-item {
    transition: none;
  }
  
  .preset-item:hover {
    transform: none;
  }
  
  .collapse-btn {
    transition: none;
  }
  
  .preset-thumbnail {
    transition: none;
  }
}

/* تحسينات إمكانية الوصول */
.preset-item:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

.action-btn:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

.preset-header:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

/* شريط التمرير المخصص */
.preset-content::-webkit-scrollbar {
  width: 6px;
}

.preset-content::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.preset-content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 3px;
}

.preset-content::-webkit-scrollbar-thumb:hover {
  background: #666;
}
