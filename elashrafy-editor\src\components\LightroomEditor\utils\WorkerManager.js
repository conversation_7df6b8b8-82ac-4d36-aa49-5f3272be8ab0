// مدير Web Workers لمعالجة الصور

export class WorkerManager {
  constructor() {
    this.worker = null;
    this.isSupported = typeof Worker !== 'undefined';
    this.currentJobId = 0;
    this.activeJobs = new Map();
  }

  // إنشاء Worker جديد
  createWorker() {
    if (!this.isSupported) {
      throw new Error('Web Workers غير مدعومة في هذا المتصفح');
    }

    try {
      // إنشاء Worker من الكود المضمن
      const workerCode = `
        // دوال مساعدة لتحويل الألوان
        function rgbToHsl(r, g, b) {
          r /= 255;
          g /= 255;
          b /= 255;
          
          const max = Math.max(r, g, b);
          const min = Math.min(r, g, b);
          let h, s, l = (max + min) / 2;
          
          if (max === min) {
            h = s = 0;
          } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            
            switch (max) {
              case r: h = (g - b) / d + (g < b ? 6 : 0); break;
              case g: h = (b - r) / d + 2; break;
              case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
          }
          
          return [h, s, l];
        }

        function hslToRgb(h, s, l) {
          let r, g, b;
          
          if (s === 0) {
            r = g = b = l;
          } else {
            const hue2rgb = (p, q, t) => {
              if (t < 0) t += 1;
              if (t > 1) t -= 1;
              if (t < 1/6) return p + (q - p) * 6 * t;
              if (t < 1/2) return q;
              if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
              return p;
            };
            
            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            const p = 2 * l - q;
            r = hue2rgb(p, q, h + 1/3);
            g = hue2rgb(p, q, h);
            b = hue2rgb(p, q, h - 1/3);
          }
          
          return [r * 255, g * 255, b * 255];
        }

        // معالجة البيانات
        function processImageData(imageData, adjustments, onProgress) {
          const data = imageData.data;
          const length = data.length;
          const totalPixels = length / 4;
          let processedPixels = 0;
          const progressInterval = Math.floor(totalPixels / 20);
          
          for (let i = 0; i < length; i += 4) {
            let r = data[i];
            let g = data[i + 1];
            let b = data[i + 2];
            const a = data[i + 3];
            
            if (a === 0) continue;
            
            // تطبيق التعديلات (نفس الكود من ImageProcessor)
            if (adjustments.exposure && adjustments.exposure !== 0) {
              const exposureFactor = Math.pow(2, adjustments.exposure);
              r *= exposureFactor;
              g *= exposureFactor;
              b *= exposureFactor;
            }
            
            if (adjustments.brightness && adjustments.brightness !== 0) {
              const brightnessFactor = adjustments.brightness * 2.55;
              r += brightnessFactor;
              g += brightnessFactor;
              b += brightnessFactor;
            }
            
            if (adjustments.contrast && adjustments.contrast !== 0) {
              const contrastFactor = (259 * (adjustments.contrast + 255)) / (255 * (259 - adjustments.contrast));
              r = contrastFactor * (r - 128) + 128;
              g = contrastFactor * (g - 128) + 128;
              b = contrastFactor * (b - 128) + 128;
            }
            
            // تحديد القيم في النطاق الصحيح
            data[i] = Math.max(0, Math.min(255, r));
            data[i + 1] = Math.max(0, Math.min(255, g));
            data[i + 2] = Math.max(0, Math.min(255, b));
            
            processedPixels++;
            if (processedPixels % progressInterval === 0) {
              const progress = (processedPixels / totalPixels) * 100;
              onProgress(progress);
            }
          }
          
          return imageData;
        }

        self.onmessage = function(e) {
          const { type, imageData, adjustments, id } = e.data;
          
          if (type === 'PROCESS_IMAGE') {
            try {
              const processedData = processImageData(
                imageData, 
                adjustments, 
                (progress) => {
                  self.postMessage({
                    type: 'PROGRESS',
                    progress,
                    id
                  });
                }
              );
              
              self.postMessage({
                type: 'COMPLETE',
                imageData: processedData,
                id
              });
            } catch (error) {
              self.postMessage({
                type: 'ERROR',
                error: error.message,
                id
              });
            }
          }
        };
      `;

      const blob = new Blob([workerCode], { type: 'application/javascript' });
      this.worker = new Worker(URL.createObjectURL(blob));
      
      this.worker.onmessage = (e) => {
        this.handleWorkerMessage(e);
      };

      this.worker.onerror = (error) => {
        console.error('خطأ في Worker:', error);
      };

    } catch (error) {
      console.error('فشل في إنشاء Worker:', error);
      this.worker = null;
    }
  }

  // معالجة رسائل Worker
  handleWorkerMessage(e) {
    const { type, id, progress, imageData, error } = e.data;
    const job = this.activeJobs.get(id);
    
    if (!job) return;

    switch (type) {
      case 'PROGRESS':
        if (job.onProgress) {
          job.onProgress(progress, 'معالجة في الخلفية...');
        }
        break;
        
      case 'COMPLETE':
        if (job.onComplete) {
          job.onComplete(imageData);
        }
        this.activeJobs.delete(id);
        break;
        
      case 'ERROR':
        if (job.onError) {
          job.onError(new Error(error));
        }
        this.activeJobs.delete(id);
        break;
    }
  }

  // معالجة صورة باستخدام Worker
  async processImageWithWorker(imageData, adjustments, onProgress) {
    if (!this.worker) {
      this.createWorker();
    }

    if (!this.worker) {
      throw new Error('فشل في إنشاء Worker');
    }

    return new Promise((resolve, reject) => {
      const jobId = ++this.currentJobId;
      
      this.activeJobs.set(jobId, {
        onProgress,
        onComplete: resolve,
        onError: reject
      });

      this.worker.postMessage({
        type: 'PROCESS_IMAGE',
        imageData,
        adjustments,
        id: jobId
      });
    });
  }

  // إلغاء جميع المهام
  cancelAllJobs() {
    this.activeJobs.clear();
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
  }

  // تنظيف الموارد
  cleanup() {
    this.cancelAllJobs();
  }

  // التحقق من دعم Workers
  isWorkerSupported() {
    return this.isSupported;
  }
}
