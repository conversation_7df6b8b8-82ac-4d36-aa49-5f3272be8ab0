import React, { useState, useEffect, useRef } from 'react';
import './SearchReplace.css';

const SearchReplace = ({ 
  isOpen, 
  onClose, 
  onSearch, 
  onReplace, 
  onReplaceAll,
  searchTerm,
  setSearchTerm,
  replaceTerm,
  setReplaceTerm,
  showReplace = false 
}) => {
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [wholeWord, setWholeWord] = useState(false);
  const [useRegex, setUseRegex] = useState(false);
  const searchInputRef = useRef(null);

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
      searchInputRef.current.select();
    }
  }, [isOpen]);

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter') {
      if (e.shiftKey) {
        // البحث للخلف
        onSearch(searchTerm, { caseSensitive, wholeWord, useRegex, backward: true });
      } else {
        // البحث للأمام
        onSearch(searchTerm, { caseSensitive, wholeWord, useRegex, backward: false });
      }
    }
  };

  const handleSearch = (backward = false) => {
    if (searchTerm.trim()) {
      onSearch(searchTerm, { caseSensitive, wholeWord, useRegex, backward });
    }
  };

  const handleReplace = () => {
    if (searchTerm.trim()) {
      onReplace(searchTerm, replaceTerm, { caseSensitive, wholeWord, useRegex });
    }
  };

  const handleReplaceAll = () => {
    if (searchTerm.trim()) {
      onReplaceAll(searchTerm, replaceTerm, { caseSensitive, wholeWord, useRegex });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="search-replace-overlay">
      <div className="search-replace-panel">
        <div className="panel-header">
          <h3>{showReplace ? 'بحث واستبدال' : 'بحث'}</h3>
          <button className="close-btn" onClick={onClose} title="إغلاق (Esc)">
            ✕
          </button>
        </div>

        <div className="search-controls">
          <div className="input-group">
            <label>البحث عن:</label>
            <div className="input-with-buttons">
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="أدخل النص للبحث عنه..."
                className="search-input"
              />
              <div className="search-buttons">
                <button 
                  onClick={() => handleSearch(true)} 
                  title="البحث السابق (Shift+Enter)"
                  className="nav-btn"
                >
                  ↑
                </button>
                <button 
                  onClick={() => handleSearch(false)} 
                  title="البحث التالي (Enter)"
                  className="nav-btn"
                >
                  ↓
                </button>
              </div>
            </div>
          </div>

          {showReplace && (
            <div className="input-group">
              <label>استبدال بـ:</label>
              <div className="input-with-buttons">
                <input
                  type="text"
                  value={replaceTerm}
                  onChange={(e) => setReplaceTerm(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="أدخل النص البديل..."
                  className="replace-input"
                />
                <div className="replace-buttons">
                  <button 
                    onClick={handleReplace}
                    className="replace-btn"
                    title="استبدال"
                  >
                    استبدال
                  </button>
                  <button 
                    onClick={handleReplaceAll}
                    className="replace-all-btn"
                    title="استبدال الكل"
                  >
                    استبدال الكل
                  </button>
                </div>
              </div>
            </div>
          )}

          <div className="options-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={caseSensitive}
                onChange={(e) => setCaseSensitive(e.target.checked)}
              />
              <span>حساس لحالة الأحرف</span>
            </label>
            
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={wholeWord}
                onChange={(e) => setWholeWord(e.target.checked)}
              />
              <span>كلمة كاملة فقط</span>
            </label>
            
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={useRegex}
                onChange={(e) => setUseRegex(e.target.checked)}
              />
              <span>استخدام التعبيرات النمطية</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchReplace;
