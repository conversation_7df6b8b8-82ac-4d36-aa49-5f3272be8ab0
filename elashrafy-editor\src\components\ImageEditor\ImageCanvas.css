.image-canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: 
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
    linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  border-radius: 8px;
  overflow: auto;
}

.image-canvas {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  cursor: crosshair;
  background: white;
}

.image-canvas.tool-select {
  cursor: default;
}

.image-canvas.tool-brush {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="8" fill="none" stroke="black" stroke-width="2"/></svg>') 10 10, auto;
}

.image-canvas.tool-eraser {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><rect x="2" y="2" width="16" height="16" fill="none" stroke="red" stroke-width="2"/></svg>') 10 10, auto;
}

.image-canvas.tool-text {
  cursor: text;
}

.image-canvas.tool-crop {
  cursor: crop;
}

.canvas-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #6c757d;
  pointer-events: none;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
}

.placeholder-icon {
  font-size: 48px;
  opacity: 0.5;
}

.placeholder-content p {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

/* تأثيرات التفاعل */
.image-canvas-container:hover .placeholder-content {
  border-color: #007acc;
  background: rgba(0, 122, 204, 0.05);
}

.image-canvas-container:hover .placeholder-icon {
  opacity: 0.7;
}

/* أدوات القص */
.crop-overlay {
  position: absolute;
  border: 2px dashed #007acc;
  background: rgba(0, 122, 204, 0.1);
  pointer-events: none;
}

.crop-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #007acc;
  border: 1px solid white;
  border-radius: 50%;
  cursor: pointer;
  pointer-events: all;
}

.crop-handle.top-left {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.crop-handle.top-right {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.crop-handle.bottom-left {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.crop-handle.bottom-right {
  bottom: -4px;
  right: -4px;
  cursor: se-resize;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .image-canvas-container {
    background-size: 15px 15px;
    background-position: 0 0, 0 7.5px, 7.5px -7.5px, -7.5px 0px;
  }
  
  .placeholder-content {
    padding: 20px;
    gap: 12px;
  }
  
  .placeholder-icon {
    font-size: 36px;
  }
  
  .placeholder-content p {
    font-size: 14px;
  }
  
  .crop-handle {
    width: 12px;
    height: 12px;
  }
  
  .crop-handle.top-left,
  .crop-handle.top-right,
  .crop-handle.bottom-left,
  .crop-handle.bottom-right {
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
  }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .image-canvas-container {
    background: 
      linear-gradient(45deg, #3a3a3a 25%, transparent 25%), 
      linear-gradient(-45deg, #3a3a3a 25%, transparent 25%), 
      linear-gradient(45deg, transparent 75%, #3a3a3a 75%), 
      linear-gradient(-45deg, transparent 75%, #3a3a3a 75%);
  }
  
  .placeholder-content {
    background: rgba(45, 45, 48, 0.8);
    border-color: #3e3e42;
    color: #cccccc;
  }
  
  .image-canvas-container:hover .placeholder-content {
    border-color: #007acc;
    background: rgba(0, 122, 204, 0.1);
  }
}

/* تأثيرات الأداء */
.image-canvas {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* تحسينات إمكانية الوصول */
.image-canvas:focus {
  outline: 2px solid #007acc;
  outline-offset: 2px;
}

/* تأثيرات التحميل */
.image-canvas-container.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007acc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}
