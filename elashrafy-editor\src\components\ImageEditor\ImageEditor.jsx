import React, { useState, useRef, useEffect, useCallback } from 'react';
import ImageCanvas from './ImageCanvas';
import EnhancedToolbar from './EnhancedToolbar';
import MultiImageManager from './MultiImageManager';
import './ImageEditor.css';

const ImageEditor = ({ 
  isOpen, 
  onClose, 
  onSave,
  initialImage = null 
}) => {
  // حالة الصورة الحالية
  const [currentImage, setCurrentImage] = useState(null);
  const [imageHistory, setImageHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  
  // حالة الأدوات
  const [selectedTool, setSelectedTool] = useState('select');
  const [toolSettings, setToolSettings] = useState({
    brushSize: 5,
    brushColor: '#000000',
    textSize: 16,
    textColor: '#000000',
    textFont: 'Arial',
    shapeColor: '#000000',
    shapeStroke: 2,
    shapeFill: false
  });
  
  // حالة التحويلات
  const [transformations, setTransformations] = useState({
    rotation: 0,
    scaleX: 1,
    scaleY: 1,
    flipX: false,
    flipY: false,
    brightness: 0,
    contrast: 0,
    saturation: 0,
    hue: 0
  });
  
  // حالة القص
  const [cropArea, setCropArea] = useState(null);
  const [isCropping, setIsCropping] = useState(false);
  
  // مراجع
  const canvasRef = useRef(null);
  const fileInputRef = useRef(null);
  
  // تحميل الصورة الأولية
  useEffect(() => {
    if (initialImage && isOpen) {
      loadImage(initialImage);
    }
  }, [initialImage, isOpen]);
  
  // تحميل صورة جديدة
  const loadImage = useCallback((imageSource) => {
    const img = new Image();
    img.onload = () => {
      setCurrentImage(img);
      addToHistory(img);
      resetTransformations();
    };

    img.onerror = () => {
      alert('فشل في تحميل الصورة. تأكد من أن الملف صورة صحيحة.');
    };

    if (typeof imageSource === 'string') {
      img.src = imageSource;
    } else if (imageSource instanceof File) {
      // التحقق من نوع الملف
      if (!imageSource.type.startsWith('image/')) {
        alert('يرجى اختيار ملف صورة صحيح.');
        return;
      }

      // التحقق من حجم الملف (أقل من 10MB)
      if (imageSource.size > 10 * 1024 * 1024) {
        alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 10MB.');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target.result;
      };
      reader.onerror = () => {
        alert('فشل في قراءة الملف.');
      };
      reader.readAsDataURL(imageSource);
    }
  }, []);
  
  // إضافة إلى التاريخ
  const addToHistory = useCallback((imageData) => {
    const newHistory = imageHistory.slice(0, historyIndex + 1);
    newHistory.push(imageData);
    setImageHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [imageHistory, historyIndex]);
  
  // إعادة تعيين التحويلات
  const resetTransformations = () => {
    setTransformations({
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
      brightness: 0,
      contrast: 0,
      saturation: 0,
      hue: 0
    });
  };
  
  // التراجع والإعادة
  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setCurrentImage(imageHistory[historyIndex - 1]);
    }
  };
  
  const redo = () => {
    if (historyIndex < imageHistory.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setCurrentImage(imageHistory[historyIndex + 1]);
    }
  };
  
  // فتح ملف
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      loadImage(file);
    }
    event.target.value = '';
  };
  
  // حفظ الصورة
  const handleSave = () => {
    if (canvasRef.current && currentImage) {
      const canvas = canvasRef.current.getCanvas();
      canvas.toBlob((blob) => {
        onSave && onSave(blob, canvas.toDataURL());
      }, 'image/png', 1.0);
    }
  };

  // تطبيق القص
  const handleCrop = () => {
    if (canvasRef.current && cropArea) {
      const { startX, startY, endX, endY } = cropArea;
      const cropData = {
        x: Math.min(startX, endX),
        y: Math.min(startY, endY),
        width: Math.abs(endX - startX),
        height: Math.abs(endY - startY)
      };

      if (cropData.width > 10 && cropData.height > 10) {
        canvasRef.current.cropImage(cropData);
        setCropArea(null);
        setIsCropping(false);
      }
    }
  };
  
  // إغلاق المحرر
  const handleClose = () => {
    setCurrentImage(null);
    setImageHistory([]);
    setHistoryIndex(-1);
    resetTransformations();
    onClose && onClose();
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="image-editor-overlay">
      <div className="image-editor">
        <div className="image-editor-header">
          <h2>محرر الصور</h2>
          <div className="header-actions">
            <button onClick={() => fileInputRef.current?.click()}>
              📁 فتح صورة
            </button>
            <button onClick={handleSave} disabled={!currentImage}>
              💾 حفظ
            </button>
            {isCropping && cropArea && (
              <button onClick={handleCrop} className="crop-apply-btn">
                ✂️ تطبيق القص
              </button>
            )}
            <button onClick={handleClose}>
              ✕ إغلاق
            </button>
          </div>
        </div>
        
        <div className="image-editor-content">
          <EnhancedToolbar
            selectedTool={selectedTool}
            onToolSelect={setSelectedTool}
            onUndo={undo}
            onRedo={redo}
            onSave={handleSave}
            onOpenImage={() => fileInputRef.current?.click()}
            transformations={transformations}
            onTransformationsChange={setTransformations}
            onApplyTransformations={() => {
              if (canvasRef.current && currentImage) {
                console.log('تطبيق التحويلات:', transformations);
                canvasRef.current.applyTransformations();
              }
            }}
            canUndo={historyIndex > 0}
            canRedo={historyIndex < imageHistory.length - 1}
          />
          
          <div className="image-editor-main">
            <div className="canvas-container">
              <ImageCanvas
                ref={canvasRef}
                image={currentImage}
                selectedTool={selectedTool}
                toolSettings={toolSettings}
                transformations={transformations}
                cropArea={cropArea}
                onCropAreaChange={setCropArea}
                onImageChange={addToHistory}
              />
            </div>
            

          </div>
        </div>
        
        <input
          type="file"
          ref={fileInputRef}
          style={{ display: 'none' }}
          accept="image/*"
          onChange={handleFileSelect}
        />
      </div>
    </div>
  );
};

export default ImageEditor;
