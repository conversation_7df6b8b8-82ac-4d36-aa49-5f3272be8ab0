import React, { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import './TextEditor.css';

const TextEditor = forwardRef(({
  content,
  onChange,
  onSelectionChange,
  onPaste,
  searchTerm,
  replaceMode
}, ref) => {
  const textareaRef = useRef(null);

  // تعريض المرجع للمكون الأب
  useImperativeHandle(ref, () => textareaRef.current);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  const handleChange = (e) => {
    onChange(e.target.value);
  };

  const handleSelectionChange = () => {
    if (textareaRef.current && onSelectionChange) {
      const { selectionStart, selectionEnd } = textareaRef.current;
      onSelectionChange(selectionStart, selectionEnd);
    }
  };

  const handleKeyDown = (e) => {
    // التعامل مع اختصارات لوحة المفاتيح
    if (e.ctrlKey) {
      switch (e.key) {
        case 'a':
          e.preventDefault();
          textareaRef.current.select();
          break;
        case 'Tab':
          e.preventDefault();
          const start = textareaRef.current.selectionStart;
          const end = textareaRef.current.selectionEnd;
          const newValue = content.substring(0, start) + '\t' + content.substring(end);
          onChange(newValue);
          setTimeout(() => {
            textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 1;
          }, 0);
          break;
        default:
          break;
      }
    }

    // إدراج Tab عند الضغط على Tab
    if (e.key === 'Tab' && !e.ctrlKey) {
      e.preventDefault();
      const start = textareaRef.current.selectionStart;
      const end = textareaRef.current.selectionEnd;
      const newValue = content.substring(0, start) + '\t' + content.substring(end);
      onChange(newValue);
      setTimeout(() => {
        textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 1;
      }, 0);
    }

    // دعم أفضل للنصوص العربية
    if (e.key === 'Enter') {
      // الحفاظ على المسافة البادئة في السطر الجديد
      const start = textareaRef.current.selectionStart;
      const lines = content.substring(0, start).split('\n');
      const currentLine = lines[lines.length - 1];
      const indent = currentLine.match(/^[\s\t]*/)[0];

      if (indent) {
        e.preventDefault();
        const newValue = content.substring(0, start) + '\n' + indent + content.substring(textareaRef.current.selectionEnd);
        onChange(newValue);
        setTimeout(() => {
          const newPosition = start + 1 + indent.length;
          textareaRef.current.selectionStart = textareaRef.current.selectionEnd = newPosition;
        }, 0);
      }
    }
  };

  // تمييز النص المطلوب البحث عنه
  const highlightSearchTerm = () => {
    if (!searchTerm || !content) return content;
    
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return content.replace(regex, '<mark>$1</mark>');
  };

  return (
    <div className="text-editor-container">
      <div className="editor-info">
        <span className="line-count">
          الأسطر: {content.split('\n').length} | 
          الأحرف: {content.length} |
          الكلمات: {content.trim() ? content.trim().split(/\s+/).length : 0}
        </span>
      </div>
      
      <textarea
        ref={textareaRef}
        className="text-editor"
        value={content}
        onChange={handleChange}
        onSelect={handleSelectionChange}
        onKeyDown={handleKeyDown}
        onPaste={onPaste}
        placeholder="ابدأ الكتابة هنا..."
        spellCheck="false"
        dir="auto"
      />
      
      {searchTerm && (
        <div className="search-highlight-overlay">
          <div 
            className="highlight-content"
            dangerouslySetInnerHTML={{ __html: highlightSearchTerm() }}
          />
        </div>
      )}
    </div>
  );
});

export default TextEditor;
