export class ImageProcessor {
  constructor() {
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d');
    this.originalImageData = null;
    this.isProcessing = false;
    this.abortController = null;

    // تحسين جودة الرسم
    this.ctx.imageSmoothingEnabled = true;
    this.ctx.imageSmoothingQuality = 'high';

    // إعدادات الأداء
    this.maxImageSize = 4096; // الحد الأقصى لحجم الصورة
    this.processingTimeout = 30000; // 30 ثانية timeout
  }

  async processImage(image, adjustments) {
    // منع المعالجة المتعددة
    if (this.isProcessing) {
      return image; // إرجاع الصورة الأصلية فوراً
    }

    this.isProcessing = true;

    return new Promise((resolve, reject) => {

      try {
        // إعداد سريع للكانفاس
        const imageWidth = image.naturalWidth || image.width;
        const imageHeight = image.naturalHeight || image.height;

        this.canvas.width = imageWidth;
        this.canvas.height = imageHeight;

        // رسم الصورة
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.drawImage(image, 0, 0, this.canvas.width, this.canvas.height);

        // حفظ البيانات الأصلية
        if (!this.originalImageData ||
            this.originalImageData.width !== this.canvas.width ||
            this.originalImageData.height !== this.canvas.height) {
          this.originalImageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        }

        // معالجة سريعة
        const imageData = new ImageData(
          new Uint8ClampedArray(this.originalImageData.data),
          this.canvas.width,
          this.canvas.height
        );

        this.applyAdjustments(imageData.data, adjustments);
        this.ctx.putImageData(imageData, 0, 0);
        this.applyTransforms(adjustments);

        // إنشاء الصورة النهائية فوراً
        this.canvas.toBlob((blob) => {
          this.isProcessing = false;

          if (blob) {
            const url = URL.createObjectURL(blob);
            const processedImage = new Image();
            processedImage.onload = () => {
              URL.revokeObjectURL(url);
              resolve(processedImage);
            };
            processedImage.onerror = () => {
              URL.revokeObjectURL(url);
              reject(new Error('فشل في تحميل الصورة'));
            };
            processedImage.src = url;
          } else {
            reject(new Error('فشل في إنشاء الصورة'));
          }
        }, 'image/png', 0.95);

      } catch (error) {
        this.isProcessing = false;
        console.error('خطأ في معالجة الصورة:', error);
        reject(error);
      }
    });
  }

  // إعادة تعيين البيانات الأصلية عند تحميل صورة جديدة
  resetOriginalData() {
    this.originalImageData = null;
    this.isProcessing = false;
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  // إلغاء المعالجة الحالية
  cancelProcessing() {
    if (this.isProcessing && this.abortController) {
      this.abortController.abort();
      this.isProcessing = false;
      return true;
    }
    return false;
  }

  // التحقق من حالة المعالجة
  getProcessingStatus() {
    return {
      isProcessing: this.isProcessing,
      canCancel: this.isProcessing && this.abortController
    };
  }

  // تنظيف شامل للموارد
  cleanup() {
    this.resetOriginalData();

    // تنظيف الكانفاس
    if (this.canvas) {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.canvas.width = 0;
      this.canvas.height = 0;
    }
  }

  applyAdjustments(data, adjustments) {
    const length = data.length;

    for (let i = 0; i < length; i += 4) {
      let r = data[i];
      let g = data[i + 1];
      let b = data[i + 2];
      const a = data[i + 3];

      // تخطي البكسلات الشفافة
      if (a === 0) continue;

      // تطبيق التعرض
      if (adjustments.exposure && adjustments.exposure !== 0) {
        const exposureFactor = Math.pow(2, adjustments.exposure);
        r *= exposureFactor;
        g *= exposureFactor;
        b *= exposureFactor;
      }

      // تطبيق السطوع
      if (adjustments.brightness && adjustments.brightness !== 0) {
        const brightnessFactor = adjustments.brightness * 2.55;
        r += brightnessFactor;
        g += brightnessFactor;
        b += brightnessFactor;
      }

      // تطبيق التباين
      if (adjustments.contrast && adjustments.contrast !== 0) {
        const contrastFactor = (259 * (adjustments.contrast + 255)) / (255 * (259 - adjustments.contrast));
        r = contrastFactor * (r - 128) + 128;
        g = contrastFactor * (g - 128) + 128;
        b = contrastFactor * (b - 128) + 128;
      }
      
      // تطبيق الإضاءات والظلال بطريقة محسنة
      const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

      if (adjustments.highlights !== 0) {
        const highlightStrength = adjustments.highlights / 100;
        if (luminance > 128) {
          const mask = Math.pow((luminance - 128) / 127, 0.5);
          const factor = 1 + (highlightStrength * mask);
          r = Math.max(0, Math.min(255, r * factor));
          g = Math.max(0, Math.min(255, g * factor));
          b = Math.max(0, Math.min(255, b * factor));
        }
      }

      if (adjustments.shadows !== 0) {
        const shadowStrength = adjustments.shadows / 100;
        if (luminance < 128) {
          const mask = Math.pow((128 - luminance) / 128, 0.5);
          const factor = 1 + (shadowStrength * mask);
          r = Math.max(0, Math.min(255, r * factor));
          g = Math.max(0, Math.min(255, g * factor));
          b = Math.max(0, Math.min(255, b * factor));
        }
      }
      
      // تطبيق النقاط البيضاء والسوداء
      if (adjustments.whites !== 0) {
        const whitesFactor = adjustments.whites / 100;
        r = r + (255 - r) * whitesFactor;
        g = g + (255 - g) * whitesFactor;
        b = b + (255 - b) * whitesFactor;
      }
      
      if (adjustments.blacks !== 0) {
        const blacksFactor = adjustments.blacks / 100;
        r = r + r * blacksFactor;
        g = g + g * blacksFactor;
        b = b + b * blacksFactor;
      }
      
      // تطبيق توازن الأبيض بطريقة محسنة
      if (adjustments.temperature !== 0) {
        const tempStrength = adjustments.temperature / 100;
        // تطبيق تأثير درجة الحرارة بناءً على اللون الأصلي
        if (tempStrength > 0) {
          // دافئ - زيادة الأحمر وتقليل الأزرق
          r = Math.max(0, Math.min(255, r + (tempStrength * 40 * (r / 255))));
          b = Math.max(0, Math.min(255, b - (tempStrength * 25 * (b / 255))));
        } else {
          // بارد - زيادة الأزرق وتقليل الأحمر
          r = Math.max(0, Math.min(255, r + (tempStrength * 25 * (r / 255))));
          b = Math.max(0, Math.min(255, b - (tempStrength * 40 * (b / 255))));
        }
      }

      if (adjustments.tint !== 0) {
        const tintStrength = adjustments.tint / 100;
        // تطبيق تأثير الصبغة على الأخضر والأرجواني
        if (tintStrength > 0) {
          g = Math.max(0, Math.min(255, g + (tintStrength * 30 * (g / 255))));
        } else {
          r = Math.max(0, Math.min(255, r - (tintStrength * 15 * (r / 255))));
          b = Math.max(0, Math.min(255, b - (tintStrength * 15 * (b / 255))));
        }
      }
      
      // تطبيق التشبع والحيوية
      if ((adjustments.saturation && adjustments.saturation !== 0) || (adjustments.vibrance && adjustments.vibrance !== 0)) {
        const [h, s, l] = this.rgbToHsl(r, g, b);

        let newS = s;

        // تطبيق التشبع العام
        if (adjustments.saturation && adjustments.saturation !== 0) {
          const saturationFactor = adjustments.saturation / 100;
          newS = s * (1 + saturationFactor);
        }

        // تطبيق الحيوية
        if (adjustments.vibrance && adjustments.vibrance !== 0) {
          const vibranceFactor = adjustments.vibrance / 100;
          const vibranceBoost = (1 - s) * vibranceFactor;
          newS += vibranceBoost;
        }

        newS = Math.max(0, Math.min(1, newS));
        const [newR, newG, newB] = this.hslToRgb(h, newS, l);
        r = newR;
        g = newG;
        b = newB;
      }
      
      // تطبيق الوضوح
      if (adjustments.clarity && adjustments.clarity !== 0) {
        const clarityFactor = adjustments.clarity / 100;
        const avg = (r + g + b) / 3;
        r = avg + (r - avg) * (1 + clarityFactor);
        g = avg + (g - avg) * (1 + clarityFactor);
        b = avg + (b - avg) * (1 + clarityFactor);
      }

      // تطبيق إزالة الضباب
      if (adjustments.dehaze && adjustments.dehaze !== 0) {
        const dehazeFactor = 1 + (adjustments.dehaze / 100);
        r *= dehazeFactor;
        g *= dehazeFactor;
        b *= dehazeFactor;
      }
      
      // تطبيق تعديلات HSL المحددة لكل لون
      [r, g, b] = this.applyHSLAdjustments(r, g, b, adjustments);

      // تحديد القيم في النطاق الصحيح
      data[i] = Math.max(0, Math.min(255, r));
      data[i + 1] = Math.max(0, Math.min(255, g));
      data[i + 2] = Math.max(0, Math.min(255, b));
    }
    
    // تطبيق إزالة الضوضاء
    if (adjustments.noiseReduction && adjustments.noiseReduction > 0) {
      this.applyNoiseReduction(data, adjustments.noiseReduction);
    }

    // تطبيق زيادة الحدة
    if (adjustments.sharpening && adjustments.sharpening > 0) {
      this.applySharpening(data, adjustments.sharpening);
    }
  }

  applyTransforms(adjustments) {
    if ((adjustments.rotation && adjustments.rotation !== 0) ||
        adjustments.flipHorizontal ||
        adjustments.flipVertical) {

      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');

      tempCanvas.width = this.canvas.width;
      tempCanvas.height = this.canvas.height;
      tempCtx.drawImage(this.canvas, 0, 0);

      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx.save();

      // تطبيق التحويلات
      this.ctx.translate(this.canvas.width / 2, this.canvas.height / 2);

      if (adjustments.rotation && adjustments.rotation !== 0) {
        this.ctx.rotate((adjustments.rotation * Math.PI) / 180);
      }

      let scaleX = adjustments.flipHorizontal ? -1 : 1;
      let scaleY = adjustments.flipVertical ? -1 : 1;
      this.ctx.scale(scaleX, scaleY);

      this.ctx.drawImage(tempCanvas, -this.canvas.width / 2, -this.canvas.height / 2);
      this.ctx.restore();
    }
  }

  applyNoiseReduction(data, strength) {
    const factor = strength / 100;
    const width = this.canvas.width;
    const height = this.canvas.height;
    
    // تطبيق مرشح تنعيم بسيط
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;
        
        for (let c = 0; c < 3; c++) {
          let sum = 0;
          let count = 0;
          
          for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
              const neighborIdx = ((y + dy) * width + (x + dx)) * 4 + c;
              sum += data[neighborIdx];
              count++;
            }
          }
          
          const avg = sum / count;
          data[idx + c] = data[idx + c] * (1 - factor) + avg * factor;
        }
      }
    }
  }

  applySharpening(data, strength) {
    const factor = strength / 100;
    const width = this.canvas.width;
    const height = this.canvas.height;
    const kernel = [-1, -1, -1, -1, 9, -1, -1, -1, -1];
    
    const tempData = new Uint8ClampedArray(data);
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;
        
        for (let c = 0; c < 3; c++) {
          let sum = 0;
          let kernelIdx = 0;
          
          for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
              const neighborIdx = ((y + dy) * width + (x + dx)) * 4 + c;
              sum += tempData[neighborIdx] * kernel[kernelIdx];
              kernelIdx++;
            }
          }
          
          data[idx + c] = Math.max(0, Math.min(255, 
            tempData[idx + c] * (1 - factor) + sum * factor
          ));
        }
      }
    }
  }

  rgbToHsl(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;
    
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;
    
    if (max === min) {
      h = s = 0;
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }
    
    return [h, s, l];
  }

  hslToRgb(h, s, l) {
    let r, g, b;

    if (s === 0) {
      r = g = b = l;
    } else {
      const hue2rgb = (p, q, t) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    return [r * 255, g * 255, b * 255];
  }

  // تطبيق تعديلات HSL المحددة لكل لون
  applyHSLAdjustments(r, g, b, adjustments) {
    const [h, s, l] = this.rgbToHsl(r, g, b);

    // تحديد اللون المهيمن
    const colorRange = this.getColorRange(h);

    if (!colorRange || !adjustments.hue || !adjustments.saturationHSL || !adjustments.luminance) {
      return [r, g, b];
    }

    // الحصول على قيم التعديل للون المحدد
    const hueAdjust = adjustments.hue[colorRange] || 0;
    const satAdjust = adjustments.saturationHSL[colorRange] || 0;
    const lumAdjust = adjustments.luminance[colorRange] || 0;

    // تطبيق التعديلات
    let newH = h + (hueAdjust / 360); // تحويل من درجات إلى نسبة
    let newS = Math.max(0, Math.min(1, s + (satAdjust / 100)));
    let newL = Math.max(0, Math.min(1, l + (lumAdjust / 100)));

    // التأكد من أن التدرج في النطاق الصحيح
    if (newH < 0) newH += 1;
    if (newH > 1) newH -= 1;

    return this.hslToRgb(newH, newS, newL);
  }

  // تحديد نطاق اللون بناءً على التدرج
  getColorRange(hue) {
    const h = hue * 360; // تحويل إلى درجات

    if (h >= 345 || h < 15) return 'red';
    if (h >= 15 && h < 45) return 'orange';
    if (h >= 45 && h < 75) return 'yellow';
    if (h >= 75 && h < 165) return 'green';
    if (h >= 165 && h < 195) return 'aqua';
    if (h >= 195 && h < 285) return 'blue';
    if (h >= 285 && h < 315) return 'purple';
    if (h >= 315 && h < 345) return 'magenta';

    return null;
  }
}
